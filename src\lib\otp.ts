import { prisma } from './prisma';

export function generateOTP(): string {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

export function getOTPExpiry(): Date {
  const now = new Date();
  return new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now
}

export async function createOTP(userId: string): Promise<string> {
  // Delete any existing OTPs for this user
  await prisma.otp.deleteMany({
    where: { userId },
  });

  const code = generateOTP();
  const expiresAt = getOTPExpiry();

  await prisma.otp.create({
    data: {
      code,
      userId,
      expiresAt,
    },
  });

  return code;
}

export async function verifyOTP(identifier: string, code: string): Promise<{ valid: boolean; userId?: string }> {
  try {
    // Find user by email or contact
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: identifier },
          { contact: identifier },
        ],
      },
      include: {
        otps: {
          where: {
            code,
            expiresAt: {
              gt: new Date(),
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!user || user.otps.length === 0) {
      return { valid: false };
    }

    // Delete the used OTP
    await prisma.otp.delete({
      where: { id: user.otps[0].id },
    });

    return { valid: true, userId: user.id };
  } catch (error) {
    console.error('OTP verification error:', error);
    return { valid: false };
  }
}

export async function cleanupExpiredOTPs(): Promise<void> {
  try {
    await prisma.otp.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
  } catch (error) {
    console.error('Error cleaning up expired OTPs:', error);
  }
}

// Helper function to display OTP (for development/testing)
export function displayOTP(code: string): string {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔐 OTP Code: ${code}`);
  }
  return `Your OTP code is: ${code}`;
}
