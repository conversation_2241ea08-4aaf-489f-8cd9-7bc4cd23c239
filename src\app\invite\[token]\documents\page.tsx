'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import axios from 'axios';
import DocumentUpload from '@/components/registration/DocumentUpload';
import ToastNotification from '@/components/ToastNotification';

interface InviteData {
  id: string;
  personRole: string;
  personIndex: number;
  registrationId: string;
  inviterName: string;
  companyName: string;
  status: string;
  addressVerificationMethod?: string;
}

export default function InviteDocumentsPage() {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;
  
  const [inviteData, setInviteData] = useState<InviteData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');

  useEffect(() => {
    const validateInvite = async () => {
      try {
        const response = await axios.get(`/api/validate-invite?token=${token}`);
        
        if (response.data.success) {
          const data = response.data.data;
          console.log('Documents page - Invite data:', data);
          console.log('Documents page - Status check:', data.status, 'Expected: FORM_SUBMITTED');

          // Check if documents are already submitted
          if (data.status === 'DOCUMENTS_SUBMITTED') {
            console.log('Documents already submitted, redirecting to complete page');
            router.push(`/invite/${token}/complete`);
            return;
          }

          // Check if personal details are completed
          if (data.status !== 'FORM_SUBMITTED') {
            console.log('Redirecting back to invite page because status is not FORM_SUBMITTED');
            router.push(`/invite/${token}`);
            return;
          }

          console.log('Status check passed, showing documents page');
          setInviteData(data);
        } else {
          setError(response.data.error || 'Invalid invite link');
        }
      } catch (err: any) {
        console.error('Error validating invite:', err);
        setError(err.response?.data?.error || 'Failed to validate invite');
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      validateInvite();
    }
  }, [token, router]);

  const handleDocumentSubmitSuccess = () => {
    // Show success message and redirect
    setToastType('success');
    setToastMessage('🎉 Documents uploaded successfully! You have completed your part of the registration.');

    // Redirect to a completion page or close the tab after showing toast
    setTimeout(() => {
      router.push(`/invite/${token}/complete`);
    }, 2000);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Validating invite...</p>
        </div>
      </div>
    );
  }

  if (error || !inviteData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">❌</span>
            </div>
            <h1 className="text-xl font-bold text-gray-900 mb-2">Invalid Invite</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.close()}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Document Upload</h1>
              <p className="text-sm text-gray-600">
                {inviteData.companyName} • {inviteData.personRole}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Invited by</p>
              <p className="font-medium text-gray-900">{inviteData.inviterName}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="max-w-4xl mx-auto px-6 py-6">
        <div className="flex items-center justify-center space-x-4 mb-8">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">✓</span>
            </div>
            <span className="ml-2 text-sm font-medium text-green-600">Personal Details</span>
          </div>
          <div className="w-16 h-1 bg-blue-600"></div>
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">2</span>
            </div>
            <span className="ml-2 text-sm font-medium text-blue-600">Documents</span>
          </div>
        </div>

        {/* Document Upload Component */}
        <DocumentUpload
          personRole={inviteData.personRole}
          invitationId={inviteData.id}
          registrationId={inviteData.registrationId}
          personIndex={inviteData.personIndex}
          addressVerificationMethod={inviteData.addressVerificationMethod}
          onSubmitSuccess={handleDocumentSubmitSuccess}
          onShowToast={(message, type) => {
            setToastType(type);
            setToastMessage(message);
          }}
        />

        {/* Toast Notification */}
        <ToastNotification
          message={toastMessage}
          onClose={() => setToastMessage(null)}
          type={toastType}
          duration={5000}
        />
      </div>
    </div>
  );
}
