import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createOTP, displayOTP } from '@/lib/otp';

export async function POST(request: NextRequest) {
  try {
    const { firstName, middleName, lastName, identifier } = await request.json();

    // Validation
    if (!firstName?.trim()) {
      return NextResponse.json(
        { error: 'First name is required' },
        { status: 400 }
      );
    }

    if (!identifier?.trim()) {
      return NextResponse.json(
        { error: 'Email or contact is required' },
        { status: 400 }
      );
    }

    // Detect if identifier is email or phone number
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    const phoneRegex = /^[0-9]{10}$/;

    let email = null;
    let contact = null;

    if (emailRegex.test(identifier)) {
      email = identifier;
    } else if (phoneRegex.test(identifier)) {
      contact = identifier;
    } else {
      return NextResponse.json(
        { error: 'Please enter a valid email address or 10-digit phone number' },
        { status: 400 }
      );
    }
    

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          contact ? { contact } : {},
        ].filter(condition => Object.keys(condition).length > 0),
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email or contact' },
        { status: 409 }
      );
    }

    // Create new user
    const user = await prisma.user.create({
      data: {
        firstName,
        middleName: middleName || null,
        lastName: lastName || null,
        email: email || null,
        contact: contact || null,
        role: 'USER',
      },
    });

    // Generate OTP
    const otpCode = await createOTP(user.id);

    // In development, display OTP in console
    const otpMessage = displayOTP(otpCode);

    return NextResponse.json({
      message: 'User created successfully. OTP sent.',
      userId: user.id,
      otpMessage: process.env.NODE_ENV === 'development' ? otpMessage : undefined,
    });

  } catch (error) {
    console.error('Signup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
