import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Next.js + TypeScript base configs
  ...compat.extends("next/core-web-vitals", "next", "next/typescript"),

  // Custom rules section
  {
    files: ["**/*.{js,ts,jsx,tsx}"],
    rules: {
      semi: ["error", "always"],
      quotes: ["double", "single"],
      "no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],
      "react/jsx-uses-react": "off", // For React 17+ JSX transform
      "react/react-in-jsx-scope": "off"
    },
  },
];

export default eslintConfig;
