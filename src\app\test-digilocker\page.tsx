"use client";

import { useState } from "react";
import axios from "axios";

export default function TestDigilocker() {
  const [initResponse, setInitResponse] = useState<any>(null);
  const [verifyResponse, setVerifyResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [clientId, setClientId] = useState("");
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testInitialize = async () => {
    setLoading(true);
    addLog("Testing DigiLocker initialization...");
    
    try {
      const response = await axios.post("/api/initialize-digilocker");
      setInitResponse(response.data);
      
      if (response.data.success) {
        setClientId(response.data.clientId);
        addLog(`✅ Initialization successful. Client ID: ${response.data.clientId}`);
        addLog(`Token received: ${response.data.token?.substring(0, 20)}...`);
      } else {
        addLog(`❌ Initialization failed: ${response.data.error}`);
      }
    } catch (error: any) {
      addLog(`❌ Initialization error: ${error.message}`);
      setInitResponse({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testVerify = async () => {
    if (!clientId) {
      addLog("❌ No client ID available. Run initialization first.");
      return;
    }

    setLoading(true);
    addLog(`Testing Aadhaar verification with client ID: ${clientId}`);
    
    try {
      const response = await axios.post("/api/verify-aadhaar", { clientId });
      setVerifyResponse(response.data);
      
      if (response.data.success) {
        addLog("✅ Verification API call successful");
        if (response.data.data) {
          addLog("✅ Aadhaar data retrieved successfully");
        } else {
          addLog("⏳ Data not ready yet (this is normal for fresh requests)");
        }
      } else {
        addLog(`❌ Verification failed: ${response.data.error}`);
      }
    } catch (error: any) {
      addLog(`❌ Verification error: ${error.message}`);
      setVerifyResponse({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const openDigilockerPopup = () => {
    if (!initResponse?.success) {
      addLog("❌ No valid initialization data. Run initialization first.");
      return;
    }

    const { token, clientId } = initResponse;
    const redirectUri = encodeURIComponent(
      window.location.origin + "/digilocker-callback"
    );
    
    // Test both URL formats to see which one works
    const urlFormat1 = `https://kyc.surepass.io/api/v1/digilocker/redirect?token=${token}&client_id=${clientId}&redirect_uri=${redirectUri}`;
    const urlFormat2 = `https://sandbox.surepass.app/digilocker/auth?token=${token}&client_id=${clientId}&redirect_uri=${redirectUri}`;
    
    addLog(`🔗 Trying URL Format 1: ${urlFormat1}`);
    
    const popup = window.open(
      urlFormat1,
      "digilockerTest",
      "width=600,height=700,resizable=yes,scrollbars=yes"
    );

    if (popup) {
      addLog("✅ Popup opened successfully");
      
      // Listen for messages from the popup
      const handleMessage = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;
        
        if (event.data.type === "DIGILOCKER_SUCCESS") {
          addLog("✅ DigiLocker authentication successful!");
          addLog(`Received code: ${event.data.code}`);
        } else if (event.data.type === "DIGILOCKER_ERROR") {
          addLog(`❌ DigiLocker authentication failed: ${event.data.error}`);
        }
      };
      
      window.addEventListener("message", handleMessage);
      
      // Clean up listener when popup closes
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          window.removeEventListener("message", handleMessage);
          clearInterval(checkClosed);
          addLog("🔒 Popup closed");
        }
      }, 1000);
    } else {
      addLog("❌ Failed to open popup. Check if popups are blocked.");
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          DigiLocker Integration Test
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Controls */}
          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
              
              <div className="space-y-3">
                <button
                  onClick={testInitialize}
                  disabled={loading}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
                >
                  {loading ? "Testing..." : "1. Test Initialize DigiLocker"}
                </button>

                <button
                  onClick={openDigilockerPopup}
                  disabled={!initResponse?.success}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400"
                >
                  2. Open DigiLocker Popup
                </button>

                <button
                  onClick={testVerify}
                  disabled={!clientId || loading}
                  className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-400"
                >
                  {loading ? "Testing..." : "3. Test Verify Aadhaar"}
                </button>

                <button
                  onClick={clearLogs}
                  className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  Clear Logs
                </button>
              </div>

              {clientId && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Current Client ID:</strong> {clientId}
                  </p>
                </div>
              )}
            </div>

            {/* API Responses */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-3">API Responses</h3>
              
              {initResponse && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-700 mb-2">Initialize Response:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                    {JSON.stringify(initResponse, null, 2)}
                  </pre>
                </div>
              )}

              {verifyResponse && (
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Verify Response:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
                    {JSON.stringify(verifyResponse, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Logs */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
            <div className="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
              {logs.length === 0 ? (
                <p className="text-gray-500">No logs yet. Start testing...</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">
            Testing Instructions
          </h3>
          <ol className="list-decimal list-inside space-y-2 text-yellow-700">
            <li>First, click "Test Initialize DigiLocker" to get a token and client ID</li>
            <li>Then click "Open DigiLocker Popup" to test the authentication flow</li>
            <li>Complete the DigiLocker authentication in the popup</li>
            <li>Finally, click "Test Verify Aadhaar" to check if data was retrieved</li>
          </ol>
          <p className="mt-3 text-sm text-yellow-600">
            Check the debug logs for detailed information about each step.
          </p>
        </div>
      </div>
    </div>
  );
}
