// app/api/verify-pan/route.ts
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { panNumber } = await request.json(); // Get panNumber from frontend

    if (
      !panNumber ||
      typeof panNumber !== "string" ||
      panNumber.length !== 10
    ) {
      return NextResponse.json(
        { success: false, error: "Invalid PAN number provided." },
        { status: 400 }
      );
    }

    const surepassApiUrl =
      "https://sandbox.surepass.io/api/v1/pan/pan-comprehensive"; // Correct SurePass PAN verification endpoint

    const surepassApiKey = process.env.SUREPASS_API_TOKEN;

    if (!surepassApiKey) {
      console.error("SUREPASS_API_TOKEN environment variable is not set.");
      return NextResponse.json(
        {
          success: false,
          error: "Server configuration error: SurePass API token missing.",
        },
        { status: 500 }
      );
    }

    // Log request details for debugging
    console.log("Making request to SurePass API:");
    console.log("URL:", surepassApiUrl);
    console.log("PAN Number:", panNumber.toUpperCase());
    console.log(
      "API Token (first 50 chars):",
      surepassApiKey.substring(0, 50) + "..."
    );

    // --- CRITICAL CHANGE HERE ---
    // The 'pan-comprehensive' endpoint expects 'id_number', not 'pan_no'
    const requestBody = {
      id_number: panNumber.toUpperCase(), // CHANGED from 'pan_no' to 'id_number'
    };
    // --- END CRITICAL CHANGE ---

    console.log("Request Body:", JSON.stringify(requestBody));

    const surepassResponse = await fetch(surepassApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${surepassApiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    // Log the response for debugging
    console.log("SurePass API Response Status:", surepassResponse.status);
    console.log(
      "SurePass API Response Headers:",
      Object.fromEntries(surepassResponse.headers.entries())
    );

    // Get response text first to check if it's HTML or JSON
    const responseText = await surepassResponse.text();
    console.log(
      "SurePass API Response Text (first 500 chars):",
      responseText.substring(0, 500)
    );

    if (!surepassResponse.ok) {
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse error response as JSON:", parseError);
        return NextResponse.json(
          {
            success: false,
            error: `SurePass API returned non-JSON response or unexpected format. Status: ${surepassResponse.status}`,
            details: { responseText: responseText.substring(0, 1000) },
          },
          { status: surepassResponse.status }
        );
      }

      console.error("SurePass PAN API error:", errorData);
      return NextResponse.json(
        {
          success: false,
          error: errorData.message || "SurePass PAN verification failed.",
          details: errorData,
        },
        { status: surepassResponse.status }
      );
    }

    // Try to parse the successful response
    let surepassData;
    try {
      surepassData = JSON.parse(responseText);
    } catch (parseError) {
      console.error("Failed to parse successful response as JSON:", parseError);
      return NextResponse.json(
        {
          success: false,
          error: "SurePass API returned invalid JSON response",
          details: { responseText: responseText.substring(0, 1000) },
        },
        { status: 500 }
      );
    }

    // Check if SurePass returned data (successful response format)
    // The actual response has data directly, not wrapped in a status field
    if (surepassData.data && surepassData.data.pan_number) {
      const verifiedData = {
        name: surepassData.data.full_name,
        dateOfBirth: surepassData.data.dob || "N/A", // Using 'dob' field from actual response
        panNumber: surepassData.data.pan_number,
        aadhaarLinked: !!surepassData.data.aadhaar_linked, // Convert to boolean
        status: "Active", // Default to Active since API returned data successfully
      };

      console.log("PAN verification successful:", verifiedData);

      return NextResponse.json({
        success: true,
        data: verifiedData,
      });
    } else {
      // If SurePass API call was OK, but no data was returned
      console.log("SurePass returned no data:", surepassData);
      return NextResponse.json(
        {
          success: false,
          error: "PAN verification failed - no data returned from SurePass.",
          details: surepassData, // Include full SurePass response for debugging
        },
        { status: 200 } // Still 200 because the call was successful, but data is not verified
      );
    }
  } catch (error) {
    console.error("Internal API error during PAN verification:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error during PAN verification.",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
