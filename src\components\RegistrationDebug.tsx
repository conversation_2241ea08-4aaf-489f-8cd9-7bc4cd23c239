'use client';

import { useRegistrationStore } from '@/store/registrationStore';

export default function RegistrationDebug() {
  const { 
    selectedCompanyType, 
    persons, 
    currentStep, 
    lastSaved,
    resetForm 
  } = useRegistrationStore();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg max-w-sm text-xs z-50">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-bold">Zustand Debug</h3>
        <button
          onClick={resetForm}
          className="text-red-400 hover:text-red-300 text-xs"
          title="Clear all data"
        >
          🗑️
        </button>
      </div>
      
      <div className="space-y-1">
        <div>
          <strong>Step:</strong> {currentStep}
        </div>
        
        <div>
          <strong>Company:</strong> {selectedCompanyType?.name || 'None'}
        </div>
        
        <div>
          <strong>Persons:</strong> {persons.length}
        </div>
        
        {persons.length > 0 && (
          <div className="mt-2">
            <strong>Person Data:</strong>
            <div className="max-h-32 overflow-y-auto">
              {persons.map((person, index) => (
                <div key={index} className="text-xs text-gray-300 mt-1">
                  {index + 1}. {person.firstName} {person.lastName}
                  {person.aadhaarVerified && ' ✅A'}
                  {person.panVerified && ' ✅P'}
                  {person.dinVerified && ' ✅D'}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {lastSaved && (
          <div className="text-gray-400 text-xs mt-2">
            Last saved: {new Date(lastSaved).toLocaleTimeString()}
          </div>
        )}
      </div>
    </div>
  );
}
