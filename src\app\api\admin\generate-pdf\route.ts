import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import jsPDF from 'jspdf';

export async function POST(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { applicationId } = await request.json();

    // Fetch fresh data from database with all details
    const companyDetail = await prisma.basicCompanyDetailStep1.findUnique({
      where: { id: applicationId },
      include: {
        user: {
          select: {
            firstName: true,
            middleName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      }
    });

    const personDetails = await prisma.personDetail.findMany({
      where: { registrationId: applicationId },
      orderBy: { personIndex: 'asc' }
    });

    if (!companyDetail) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Generate PDF using jsPDF text method
    const pdfBuffer = generatePDFWithText(companyDetail, personDetails);

    // Return PDF file for direct download
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${companyDetail.companyName}_application_details.pdf"`
      }
    });

  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generatePDFWithText(companyDetail: any, personDetails: any[]): Buffer {
  const doc = new jsPDF();
  let yPosition = 20;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 20;
  const lineHeight = 7; // Increased for better readability
  const primaryColor = '#007bff'; // A nice blue for accents

  const formatVerificationData = (data: any) => {
    if (!data) return 'Not available';
    if (typeof data === 'string') return data;
    if (typeof data === 'object') {
      // Improved formatting for object data
      return Object.entries(data).map(([key, value]) => {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()); // Convert camelCase to Title Case
        return `${formattedKey}: ${value}`;
      }).join(', ');
    }
    return JSON.stringify(data);
  };

  const addText = (text: string, x: number = margin, fontSize: number = 10, style: 'normal' | 'bold' = 'normal', color: string = '#000000') => {
    // Check if we need a new page, with some buffer
    if (yPosition > pageHeight - margin - 15) { // Adjusted for more buffer
      doc.addPage();
      yPosition = margin; // Reset yPosition for new page
    }

    doc.setFontSize(fontSize);
    doc.setFont('helvetica', style);
    doc.setTextColor(color);

    // Handle long text by splitting into multiple lines
    const maxWidth = doc.internal.pageSize.width - (2 * x); // Max width for text dynamically
    const lines = doc.splitTextToSize(text, maxWidth);

    if (Array.isArray(lines)) {
      lines.forEach((line: string) => {
        doc.text(line, x, yPosition);
        yPosition += lineHeight;
      });
    } else {
      doc.text(lines, x, yPosition);
      yPosition += lineHeight;
    }
  };

  const addSection = (title: string) => {
    yPosition += 10; // More space before new section
    doc.setDrawColor(primaryColor); // Set line color
    doc.setLineWidth(0.5); // Line thickness
    doc.line(margin, yPosition, doc.internal.pageSize.width - margin, yPosition); // Draw a line
    yPosition += 5;
    addText(title, margin, 16, 'bold', primaryColor); // Larger, bold, colored title
    yPosition += 8; // More space after title
  };

  // --- Header ---
  // Adding a top line
  doc.setDrawColor(primaryColor);
  doc.setLineWidth(1);
  doc.line(margin, 15, doc.internal.pageSize.width - margin, 15);
  yPosition = 25;

  addText('APPLICATION DETAILS REPORT', margin + 45, 22, 'bold', primaryColor); // Larger, centered title
  yPosition += 8;
  addText(`Company: ${companyDetail.companyName}`, margin, 14, 'bold');
  addText(`Application ID: ${companyDetail.id}`, margin, 10);
  addText(`Generated on: ${new Date().toLocaleString()}`, margin, 10);
  yPosition += 15;

  // --- Company Information ---
  addSection('COMPANY INFORMATION');
  addText(`• Company Name: ${companyDetail.companyName}`, margin + 5);
  addText(`• Company Type: ${companyDetail.companyType || 'Not specified'}`, margin + 5);
  addText(`• Alternate Name: ${companyDetail.alternateName || 'Not provided'}`, margin + 5);
  addText(`• State: ${companyDetail.state}`, margin + 5);
  addText(`• Status: ${companyDetail.status}`, margin + 5);
  addText(`• Share Capital: ₹${companyDetail.totalShareCapital?.toLocaleString() || 'Not specified'}`, margin + 5);
  addText(`• Business Keywords: ${companyDetail.businessKeywords}`, margin + 5);
  addText(`• Director Address Same: ${companyDetail.isDirectorAddressSame === true ? 'Yes' : companyDetail.isDirectorAddressSame === false ? 'No' : 'Not specified'}`, margin + 5);

  if (companyDetail.addressProofType) {
    addText(`• Address Proof Type: ${companyDetail.addressProofType}`, margin + 5);
  }

  if (companyDetail.generatedObjective) {
    yPosition += 8;
    addText('Generated Objective:', margin + 5, 11, 'bold');
    addText(companyDetail.generatedObjective, margin + 10, 9);
  }

  // --- Applicant Information ---
  addSection('APPLICANT INFORMATION');
  addText(`• Name: ${companyDetail.user.firstName} ${companyDetail.user.middleName || ''} ${companyDetail.user.lastName || ''}`, margin + 5);
  addText(`• Email: ${companyDetail.user.email || 'Not provided'}`, margin + 5);
  addText(`• Phone: ${companyDetail.user.contact || 'Not provided'}`, margin + 5);
  // Assuming the `companyDetail` also contains direct applicant info, not just the linked user
  // If companyDetail.firstName, middleName, lastName are redundant with user, remove these next 3 lines.
  // Otherwise, they represent the person who filled out the form, not necessarily the account holder.
addText(
  `• Form Name: First Name: ${companyDetail.firstName}, Middle Name: ${companyDetail.middleName || 'N/A'}, Last Name: ${companyDetail.lastName || 'N/A'}`,
  margin + 5
);
  addText(`• Form  Email: ${companyDetail.email || 'Not provided'}`, margin + 5);
  addText(`• Form  Phone: ${companyDetail.phone || 'Not provided'}`, margin + 5);


  // --- Person Details ---
  addSection(`PERSON DETAILS (${personDetails.length} persons)`);

  personDetails.forEach((person, index) => {
    yPosition += 8; // Space before each person's details
addText(
  `${index + 1}. ${person.personRole} - First Name: ${person.firstName}, Middle Name: ${person.middleName || 'N/A'}, Last Name: ${person.lastName || 'N/A'}`,
  margin + 5,
  13,
  'bold',
  primaryColor
);

    if (person.invitationId) {
      addText(' Filled by invitation', margin + 10, 9);
    }

    addText(`• Status: ${person.isCompleted ? ' Completed' : ' In Progress'}`, margin + 10);
    addText(`• Documents: ${person.documentsCompleted ? ' Complete' : ' Pending'}`, margin + 10);

    // Basic Information
    addText('  Basic Information:', margin + 10, 10, 'bold');
    addText(`    Email: ${person.email}`, margin + 15);
    addText(`    Contact: ${person.contact}`, margin + 15);
    addText(`    Person Index: ${person.personIndex}`, margin + 15);

    if (person.sharePercentage) {
      addText(`    Share Percentage: ${person.sharePercentage}%`, margin + 15);
    }

    // Verification Status
    addText('  Verification Status:', margin + 10, 10, 'bold');
    addText(`    Aadhaar: ${person.aadhaarVerified ? ' Verified' : ' Not Verified'}`, margin + 15);
    addText(`    PAN: ${person.panVerified ? ' Verified' : ' Not Verified'}`, margin + 15);

    if (person.hasDIN) {
      addText(`    DIN: ${person.dinVerified ? ' Verified' : ' Not Verified'}`, margin + 15);
    }

    addText(`    Electricity: ${person.electricityVerified ? ' Verified' : ' Not Verified'}`, margin + 15);

    // Verification Data
    if (person.aadhaarVerified && person.aadhaarData) {
      addText('  Aadhaar Data:', margin + 10, 10, 'bold');
      addText(formatVerificationData(person.aadhaarData), margin + 15, 9);
    }

    if (person.panVerified && person.panData) {
      addText('  PAN Data:', margin + 10, 10, 'bold');
      addText(formatVerificationData(person.panData), margin + 15, 9);
    }

    if (person.dinVerified && person.dinData) {
      addText('  DIN Data:', margin + 10, 10, 'bold');
      addText(formatVerificationData(person.dinData), margin + 15, 9);
    }

    if (person.electricityVerified && person.electricityData) {
      addText('  Electricity Data:', margin + 10, 10, 'bold');
      addText(formatVerificationData(person.electricityData), margin + 15, 9);
    }

    // Timestamps
    addText(`  Created: ${new Date(person.createdAt).toLocaleString()}`, margin + 10, 9);
    addText(`  Updated: ${new Date(person.updatedAt).toLocaleString()}`, margin + 10, 9);

    yPosition += 10; // Add more space between persons
    // Add a light separator line between persons
    doc.setDrawColor('#cccccc');
    doc.setLineWidth(0.2);
    doc.line(margin + 5, yPosition, doc.internal.pageSize.width - margin - 5, yPosition);
  });

  // --- Application Timeline ---
  addSection('APPLICATION TIMELINE');
  addText(`• Created: ${new Date(companyDetail.createdAt).toLocaleString()}`, margin + 5);
  addText(`• Last Updated: ${new Date(companyDetail.updatedAt).toLocaleString()}`, margin + 5);
  yPosition += 10;

  // --- Footer ---
  doc.setDrawColor(primaryColor);
  doc.setLineWidth(1);
  doc.line(margin, pageHeight - 15, doc.internal.pageSize.width - margin, pageHeight - 15);
  addText('Report generated by [Your Company Name]', margin, 8, 'normal', '#666666');
  // doc.text('Page ' + doc.internal.getCurrentPageInfo().pageNumber, doc.internal.pageSize.width - margin - 15, pageHeight - 10);


  // Return PDF as buffer
  return Buffer.from(doc.output('arraybuffer'));
}