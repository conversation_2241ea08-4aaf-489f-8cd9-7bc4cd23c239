'use client';

interface Step {
  id: number;
  label: string;
  description?: string;
}

interface ProgressIndicatorProps {
  steps: Step[];
  currentStep: number;
  className?: string;
  showDescription?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function ProgressIndicator({
  steps,
  currentStep,
  className = '',
  showDescription = true,
  size = 'md'
}: ProgressIndicatorProps) {

  // Responsive size configurations
  const sizeConfig = {
    sm: {
      circle: 'w-6 h-6 text-xs sm:w-8 sm:h-8 sm:text-sm',
      connector: 'w-6 h-0.5 sm:w-12 sm:h-0.5',
      spacing: 'space-x-1 sm:space-x-3',
      labelSpacing: 'space-x-3 sm:space-x-6',
      labelSize: 'text-xs'
    },
    md: {
      circle: 'w-7 h-7 text-xs sm:w-10 sm:h-10 sm:text-base',
      connector: 'w-8 h-0.5 sm:w-16 sm:h-1',
      spacing: 'space-x-1 sm:space-x-4',
      labelSpacing: 'space-x-3 sm:space-x-8',
      labelSize: 'text-xs sm:text-xs'
    },
    lg: {
      circle: 'w-8 h-8 text-sm sm:w-12 sm:h-12 sm:text-lg',
      connector: 'w-10 h-1 sm:w-20 sm:h-1.5',
      spacing: 'space-x-2 sm:space-x-5',
      labelSpacing: 'space-x-4 sm:space-x-10',
      labelSize: 'text-xs sm:text-sm'
    }
  };

  const config = sizeConfig[size];
  return (
    <div className={`mb-6 sm:mb-8 ${className}`}>
      {/* Progress Circles */}
      <div className={`flex items-center justify-center ${config.spacing} overflow-x-auto`}>
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={`${config.circle} rounded-full flex items-center justify-center font-semibold transition-colors duration-200 ${step.id === currentStep
                ? 'bg-blue-600 text-white shadow-lg'
                : step.id < currentStep
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-200 text-gray-400'
              }`}>
              {step.id < currentStep ? '✓' : step.id}
            </div>
            {index < steps.length - 1 && (
              <div className={`${config.connector} mx-2 transition-colors duration-200 ${step.id < currentStep ? 'bg-green-600' : 'bg-gray-200'
                }`} />
            )}
          </div>
        ))}
      </div>

      {/* Step Labels */}
      <div className="flex justify-center mt-4">
        <div className="text-center">
          {/* Desktop: Horizontal labels */}
          <div className={`hidden sm:flex ${config.labelSpacing} mt-2 ${config.labelSize} text-gray-500`}>
            {steps.map((step) => (
              <span
                key={step.id}
                className={`transition-colors duration-200 ${currentStep === step.id
                    ? 'font-medium text-blue-600'
                    : currentStep > step.id
                      ? 'text-green-600'
                      : 'text-gray-500'
                  }`}
              >
                {step.label}
              </span>
            ))}
          </div>

          {/* Mobile: Show only current step label */}
          <div className="sm:hidden mt-2">
            <span className={`${config.labelSize} font-medium text-blue-600`}>
              {steps.find(step => step.id === currentStep)?.label}
            </span>
          </div>

          {/* Current Step Description */}
          {showDescription && steps.find(step => step.id === currentStep)?.description && (
            <p className="text-xs sm:text-sm text-gray-600 mt-2 px-4 sm:px-0">
              {steps.find(step => step.id === currentStep)?.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
