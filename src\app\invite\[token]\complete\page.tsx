'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import axios from 'axios';

interface InviteData {
  id: string;
  personRole: string;
  registrationId: string;
  inviterName: string;
  companyName: string;
  status: string;
}

export default function InviteCompletePage() {
  const params = useParams();
  const token = params.token as string;
  
  const [inviteData, setInviteData] = useState<InviteData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const validateInvite = async () => {
      try {
        const response = await axios.get(`/api/validate-invite?token=${token}`);
        
        if (response.data.success) {
          setInviteData(response.data.data);
        } else {
          setError(response.data.error || 'Invalid invite link');
        }
      } catch (err: any) {
        console.error('Error validating invite:', err);
        setError(err.response?.data?.error || 'Failed to validate invite');
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      validateInvite();
    }
  }, [token]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (error || !inviteData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-red-600 text-2xl">❌</span>
            </div>
            <h1 className="text-xl font-bold text-gray-900 mb-2">Invalid Invite</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.close()}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-green-600 text-4xl">✓</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">All Done!</h1>
          <p className="text-gray-600 mb-6">
            You have successfully completed your part of the registration for {inviteData.companyName}.
          </p>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-green-800 mb-2">What happens next?</h3>
            <ul className="text-sm text-green-700 space-y-1 text-left">
              <li>• {inviteData.inviterName} will be notified of your submission</li>
              <li>• They will complete the remaining registration steps</li>
              <li>• You'll be contacted if any additional information is needed</li>
            </ul>
          </div>
          
          <div className="flex flex-col space-y-3">
            <button
              onClick={() => window.close()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium"
            >
              Close This Window
            </button>
            
            <p className="text-sm text-gray-500">
              You can safely close this window now.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
