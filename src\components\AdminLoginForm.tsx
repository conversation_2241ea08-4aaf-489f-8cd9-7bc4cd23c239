'use client';

import { useForm } from 'react-hook-form';
import { useState } from 'react';
import axios from 'axios';

interface AdminLoginData {
  email: string;
  password: string;
}

export default function AdminLoginForm() {
  const [serverError, setServerError] = useState('');
  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AdminLoginData>();

  const onSubmit = async (data: AdminLoginData) => {
    setServerError('');
    setLoading(true);

    try {
      const response = await axios.post('/api/auth/admin/login', data, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const res = response.data;

      // Redirect on success
      window.location.href = res.redirectUrl || '/admin/dashboard';
    } catch (err: unknown) {
      let message = 'Login failed';
    
      if (axios.isAxiosError(err)) {
        message = err.response?.data?.error || err.message || 'Login failed';
      } else if (err instanceof Error) {
        message = err.message;
    }
    
      setServerError(message);
    }
    
  };

  return (
    <div className='max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md'>
      <h2 className='text-2xl font-bold mb-6 text-center'>Admin Login</h2>

      <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
        <div>
          <label htmlFor='email' className='block text-sm font-medium text-gray-700'>
            Email *
          </label>
          <input
            type='email'
            id='email'
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Enter a valid email address',
              },
            })}
            className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
              errors.email ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder='<EMAIL>'
          />
          {errors.email && (
            <p className='text-sm text-red-600 mt-1'>{errors.email.message}</p>
          )}
        </div>

        <div>
          <label htmlFor='password' className='block text-sm font-medium text-gray-700'>
            Password *
          </label>
          <input
            type='password'
            id='password'
            {...register('password', {
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            })}
            className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
              errors.password ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder='Enter your password'
          />
          {errors.password && (
            <p className='text-sm text-red-600 mt-1'>{errors.password.message}</p>
          )}
        </div>

        {serverError && (
          <div className='text-red-600 text-sm bg-red-50 p-3 rounded'>
            {serverError}
          </div>
        )}

        <button
          type='submit'
          disabled={loading}
          className='w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50'
        >
          {loading ? 'Logging in...' : 'Login'}
        </button>
      </form>

      <div className='mt-4 text-center'>
        <p className='text-sm text-gray-600'>
          <a href='/login' className='text-blue-600 hover:text-blue-500'>
            ← Back to User Login
          </a>
        </p>
      </div>

      <div className='mt-4 p-3 bg-yellow-50 rounded'>
        <p className='text-xs text-yellow-800'>
          <strong>Admin Access Only:</strong> This login is for administrators only.
          Regular users should use the standard login with OTP verification.
        </p>
      </div>
    </div>
  );
}
