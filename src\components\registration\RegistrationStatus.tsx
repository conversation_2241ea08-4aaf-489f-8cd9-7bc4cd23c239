'use client';

import { useState, useEffect } from 'react';
import { useRegistrationStore } from '@/store/registrationStore';
import axios from 'axios';

interface RegistrationStatusProps {
  registrationId: string;
  onStatusChange?: (status: 'IN_PROGRESS' | 'SUBMITTED') => void;
  onShowToast?: (message: string, type: 'success' | 'error' | 'info') => void;
}

export default function RegistrationStatus({
  registrationId,
  onStatusChange,
  onShowToast
}: RegistrationStatusProps) {
  const { registrationStatus, setRegistrationStatus } = useRegistrationStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitationStatus, setInvitationStatus] = useState<{
    pendingInvitations: Array<{ personRole: string; status: string; type: 'form' | 'documents' }>;
    canSubmit: boolean;
  }>({ pendingInvitations: [], canSubmit: true });

  // Check invitation status for form submission validation
  const checkInvitationStatus = async () => {
    try {
      const response = await axios.get(`/api/get-person-status?registrationId=${registrationId}`);

      if (response.data.success) {
        const personStatuses = response.data.data.personStatuses;
        const pendingInvitations: Array<{
          personRole: string;
          status: string;
          type: 'form' | 'documents';
        }> = [];

        // Check all persons except Person A (index 0)
        Object.keys(personStatuses).forEach(index => {
          const personIndex = parseInt(index);
          const status = personStatuses[personIndex];

          if (personIndex !== 0 && status.hasInvitation) {
            // Check for form submission pending
            if (status.inviteStatus === 'pending') {
              pendingInvitations.push({
                personRole: `Person ${personIndex + 1}`,
                status: status.inviteStatus,
                type: 'form'
              });
            }
            // Check for document submission pending
            else if (status.inviteStatus === 'form_submitted') {
              pendingInvitations.push({
                personRole: `Person ${personIndex + 1}`,
                status: status.inviteStatus,
                type: 'documents'
              });
            }
          }
        });

        setInvitationStatus({
          pendingInvitations,
          canSubmit: pendingInvitations.length === 0
        });
      }
    } catch (err) {
      console.error('Error checking invitation status:', err);
    }
  };

  // Fetch current status from server
  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/get-registration-status?registrationId=${registrationId}`);

      if (response.data.success) {
        const status = response.data.data.status;
        setRegistrationStatus(status);
        onStatusChange?.(status);
      }

      // Also check invitation status
      await checkInvitationStatus();
    } catch (err) {
      console.error('Error fetching registration status:', err);
      setError('Failed to fetch registration status');
    } finally {
      setLoading(false);
    }
  };

  // Submit registration (change status to SUBMITTED)
  const submitRegistration = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check invitation status before submitting
      await checkInvitationStatus();

      if (!invitationStatus.canSubmit) {
        const formPending = invitationStatus.pendingInvitations.filter(inv => inv.type === 'form');
        const documentsPending = invitationStatus.pendingInvitations.filter(inv => inv.type === 'documents');

        let errorMessage = 'Cannot submit registration yet. ';

        if (formPending.length > 0) {
          const pendingList = formPending.map(inv => `${inv.personRole}`).join(', ');
          errorMessage += `The following invited persons have not submitted their forms: ${pendingList}. `;
        }

        if (documentsPending.length > 0) {
          const pendingList = documentsPending.map(inv => `${inv.personRole}`).join(', ');
          errorMessage += `The following invited persons have not uploaded their documents: ${pendingList}. `;
        }

        errorMessage += 'Please wait for all invited persons to complete their submission.';

        setError(errorMessage);
        onShowToast?.(`❌ ${errorMessage}`, 'error');
        return;
      }

      const response = await axios.post('/api/submit-registration', {
        registrationId
      });

      if (response.data.success) {
        setRegistrationStatus('SUBMITTED');
        onStatusChange?.('SUBMITTED');
        onShowToast?.(' Registration submitted successfully! Now please upload your documents to complete the process.', 'success');
      }
    } catch (err: any) {
      console.error('Error submitting registration:', err);
      const errorMessage = err.response?.data?.error || 'Failed to submit registration';
      setError(errorMessage);
      onShowToast?.(`❌ ${errorMessage}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (registrationId) {
      fetchStatus();
    }
  }, [registrationId]);

  if (loading && !registrationStatus) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading status...</span>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`w-3 h-3 rounded-full ${
            registrationStatus === 'SUBMITTED' 
              ? 'bg-green-500' 
              : 'bg-yellow-500'
          }`}></div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Registration Status
            </h3>
            <p className={`text-sm ${
              registrationStatus === 'SUBMITTED' 
                ? 'text-green-600' 
                : 'text-yellow-600'
            }`}>
              {registrationStatus === 'SUBMITTED' 
                ? '✅ Submitted' 
                : '🔄 In Progress'
              }
            </p>
          </div>
        </div>

        {registrationStatus === 'IN_PROGRESS' && (
          <button
            onClick={submitRegistration}
            disabled={loading || !invitationStatus.canSubmit}
            className={`px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${
              loading || !invitationStatus.canSubmit
                ? 'bg-gray-400 cursor-not-allowed text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
            title={!invitationStatus.canSubmit ? 'Cannot submit until all invited persons complete their forms' : ''}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <span>📤</span>
                <span>Submit Registration</span>
              </>
            )}
          </button>
        )}

        {registrationStatus === 'SUBMITTED' && (
          <div className="text-green-600 font-medium flex items-center space-x-2">
            <span>✅</span>
            <span>Registration Completed</span>
          </div>
        )}
      </div>

      {/* Show invitation status warning */}
      {registrationStatus === 'IN_PROGRESS' && invitationStatus.pendingInvitations.length > 0 && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <span className="text-yellow-600 text-lg">⚠️</span>
            <div>
              <h4 className="text-yellow-800 font-medium mb-2">Waiting for Invited Persons</h4>
              <p className="text-yellow-700 text-sm mb-3">
                The following invited persons have not submitted their forms yet:
              </p>
              <ul className="text-yellow-700 text-sm space-y-1">
                {invitationStatus.pendingInvitations.map((inv, index) => (
                  <li key={index} className="flex items-center space-x-2">
                    <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                    <span>
                      {inv.personRole} -
                      {inv.type === 'form' ? ' Form not submitted' : ' Documents not uploaded'}
                      {inv.status === 'pending' ? ' (Invite sent)' :
                       inv.status === 'form_submitted' ? ' (Form done, docs pending)' : ''}
                    </span>
                  </li>
                ))}
              </ul>
              <p className="text-yellow-600 text-xs mt-3">
                You cannot submit the registration until all invited persons complete both their form submission and document upload.
              </p>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {registrationStatus === 'SUBMITTED' && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-medium text-green-800 mb-2">What's Next?</h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• Your registration has been submitted successfully</li>
            <li>• Our team will review your application</li>
            <li>• You will receive updates via email</li>
            <li>• Check your dashboard for status updates</li>
          </ul>
        </div>
      )}
    </div>
  );
}
