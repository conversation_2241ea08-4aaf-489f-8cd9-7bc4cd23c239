"use client";

import { useState, useEffect, useRef } from "react";
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  useWatch,
} from "react-hook-form";
import axios from "axios";

// Add Digiboost SDK type to window object
declare global {
  interface Window {
    DigiboostSdk: (config: {
      gateway: "sandbox" | "production";
      token: string;
      selector: string;
      onSuccess: (data: any) => void;
      onFailure: (error: any) => void;
      style?: React.CSSProperties;
    }) => void;
  }
}

// Your form structure
interface PersonFormData {
  persons: Array<{
    firstName: string;
    middleName?: string;
    lastName?: string;
    email: string;
    contact: string;
    aadhaarNumber?: string;
    aadhaarVerified: boolean;
    aadhaarData?: unknown;
    panNumber?: string;
    panVerified: boolean;
    panData?: unknown;
    hasDIN?: boolean;
    dinNumber?: string;
    dinVerified: boolean;
    dinData?: unknown;
    electricityConnectionNumber?: string;
    electricityBoardName?: string;
    electricityVerified?: boolean;
    electricityData?: unknown;
    addressVerificationMethod?: string;
  }>;
}

interface AadhaarData {
  name: string;
  fatherName: string;
  gender: string;
  aadhaarNumber: string;
  address: string;
}

interface AadhaarVerificationProps {
  index: number;
  control: Control<PersonFormData>;
  errors: FieldErrors<PersonFormData>;
  setValue: UseFormSetValue<PersonFormData>;
  onShowToast?: (message: string, type: "success" | "error" | "info") => void;
}

export default function AadhaarVerification({
  index,
  control,
  setValue,
  onShowToast,
}: AadhaarVerificationProps) {
  const [isInitializing, setIsInitializing] = useState(true);
  const [aadhaarData, setAadhaarData] = useState<AadhaarData | null>(null);
  const sdkInitialized = useRef(false);

  const verified = useWatch({
    control,
    name: `persons.${index}.aadhaarVerified`,
    defaultValue: false,
  });

  // 🔍 DEBUG: Log verification status changes
  useEffect(() => {
    console.log(
      `🔍 [AadhaarVerification] Person ${index} - Verified status:`,
      verified
    );
    console.log(
      `🔍 [AadhaarVerification] Person ${index} - Local aadhaarData:`,
      aadhaarData
    );
  }, [verified, aadhaarData, index]);

  useEffect(() => {
    if (!verified && !sdkInitialized.current) {
      initializeDigilockerSdk();
    }
  }, [verified]);

  const initializeDigilockerSdk = async () => {
    setIsInitializing(true);
    sdkInitialized.current = true;

    const containerId = `digilocker-button-container-${index}`;
    const container = document.getElementById(containerId);

    if (container) {
      // ✅ Prevent duplicate DigiLocker button
      if (container.children.length > 0) {
        setIsInitializing(false);
        return;
      }
      container.innerHTML = "";
    }

    if (typeof window.DigiboostSdk !== "function") {
      console.warn("SDK not yet available, skipping initialization.");
      setIsInitializing(false);
      return;
    }

    try {
      const response = await axios.post("/api/initialize-digilocker");
      if (!response.data.success) {
        throw new Error(
          response.data.error || "Failed to initialize Digilocker."
        );
      }

      const { token, clientId } = response.data;

      window.DigiboostSdk({
        gateway: "sandbox",
        token,
        selector: `#${containerId}`,
        style: {
          backgroundColor: "#3a4e7aff",
          color: "white",
          // padding: "12px 36px",
          // borderRadius: "8px",
          // fontSize: "16px",
          fontWeight: "500",
          border: "none",
          cursor: "pointer",
          // width: "100%",
        },
        onSuccess: () => {
          console.log(
            `🎉 [DigiLocker] Person ${index} - DigiLocker verification SUCCESS!`
          );
          onShowToast?.(
            "Digilocker flow complete! Fetching details...",
            "info"
          );
          fetchAndSetAadhaarData(clientId);
        },
        onFailure: (error: any) => {
          console.error("Digiboost Failure:", error);
          onShowToast?.("Verification was cancelled or failed.", "error");
          // ❌ Not retrying auto-initialization to prevent duplicate buttons
        },
      });
    } catch (error: any) {
      console.error("Initialization Error:", error);
      onShowToast?.(error.message || "Unexpected error", "error");
    } finally {
      setIsInitializing(false);
    }
  };

  const fetchAndSetAadhaarData = async (clientId: string) => {
    try {
      console.log(
        `🔄 [API Call] Person ${index} - Calling /api/verify-aadhaar with clientId:`,
        clientId
      );

      const response = await axios.post("/api/verify-aadhaar", { clientId });

      console.log(
        `📥 [API Response] Person ${index} - Full response:`,
        response.data
      );

      if (response.data.success && response.data.data) {
        const verifiedData = response.data.data as AadhaarData;

        console.log(
          `✅ [Data Processing] Person ${index} - Verified data:`,
          verifiedData
        );

        // Set local state
        setAadhaarData(verifiedData);

        // Set form values
        console.log(
          `📝 [Form Update] Person ${index} - Setting form values...`
        );
        setValue(`persons.${index}.aadhaarVerified`, true, {
          shouldValidate: true,
        });
        setValue(`persons.${index}.aadhaarData`, verifiedData);
        setValue(`persons.${index}.aadhaarNumber`, verifiedData.aadhaarNumber);

        console.log(
          `🎉 [Success] Person ${index} - Form values set successfully!`
        );
        onShowToast?.("Aadhaar verified successfully!", "success");
      } else {
        console.error(
          `❌ [API Error] Person ${index} - Invalid response:`,
          response.data
        );
        throw new Error("Failed to retrieve Aadhaar details.");
      }
    } catch (error: any) {
      console.error(`💥 [Error] Person ${index} - Fetch error:`, error);
      onShowToast?.(error.message || "Error verifying Aadhaar.", "error");
    }
  };

  const getStatusBadge = () => {
    return verified ? (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        ✅ Verified
      </span>
    ) : (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        ❌ Not Verified
      </span>
    );
  };

  return (
    <div className="border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <span className="font-medium text-gray-900">
            🆔 Aadhaar Verification
          </span>
          {getStatusBadge()}
        </div>
      </div>

      <div className="p-4 border-t border-gray-200">
        {!verified && (
          <div>
            {isInitializing && (
              <div className="flex items-center justify-center text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-3"></div>
                Initializing Secure Verification...
              </div>
            )}
            <div id={`digilocker-button-container-${index}`}></div>
          </div>
        )}

        {verified &&
          aadhaarData &&
          (() => {
            console.log(
              `🎨 [UI Render] Person ${index} - Rendering verified data:`,
              { verified, aadhaarData }
            );
            return (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-3">
                  Verified Aadhaar Details:
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <strong>Name:</strong> {aadhaarData.name}
                  </div>
                  <div>
                    <strong>Father's Name:</strong> {aadhaarData.fatherName}
                  </div>
                  <div>
                    <strong>Gender:</strong> {aadhaarData.gender}
                  </div>
                  <div>
                    <strong>Aadhaar Number:</strong> {aadhaarData.aadhaarNumber}
                  </div>
                  <div className="md:col-span-2">
                    <strong>Address:</strong> {aadhaarData.address}
                  </div>
                </div>
              </div>
            );
          })()}
      </div>
    </div>
  );
}
