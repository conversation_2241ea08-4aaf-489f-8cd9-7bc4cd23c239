// app/api/check-company-name/route.ts
import { NextRequest, NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai"; // Import Gemini SDK

export async function POST(request: NextRequest) {
  try {
    const { name, businessKeywords } = await request.json(); // Also expect businessKeywords from frontend

    if (!name?.trim()) {
      return NextResponse.json(
        { error: "Company name is required" },
        { status: 400 }
      );
    }

    // --- SurePass API Check ---
    const surepassApiUrl =
      "https://sandbox.surepass.io/api/v1/corporate/name-to-cin-list";
    const surepassApiKey = process.env.SUREPASS_API_TOKEN;

    if (!surepassApiKey) {
      console.error("SUREPASS_API_TOKEN environment variable is not set.");
      return NextResponse.json(
        { error: "Server configuration error: SurePass API token missing" },
        { status: 500 }
      );
    }

    const surepassResponse = await fetch(surepassApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${surepassApiKey}`,
      },
      body: JSON.stringify({
        company_name_search: name,
      }),
    });

    if (!surepassResponse.ok) {
      const errorData = await surepassResponse.json();
      console.error("SurePass API error:", errorData);
      return NextResponse.json(
        {
          error: "Error fetching company data from SurePass",
          details: errorData,
        },
        { status: surepassResponse.status }
      );
    }

    const surepassData = await surepassResponse.json();
    const companyList = surepassData.data?.company_list || [];
    const isAvailable = companyList.length === 0; // True if no companies found, false if matches found

    let message: string;
    let finalSuggestions: string[] = []; // This will hold Gemini's suggestions

    if (!isAvailable) {
      // --- Name is NOT available (SurePass found conflicts) ---
      message = "Name not available. Generating creative alternatives.";

      // --- Call Gemini for Creative Suggestions ---
      const geminiApiKey = process.env.GEMINI_API_KEY;

      if (!geminiApiKey) {
        console.error("GEMINI_API_KEY environment variable is not set.");
        // Even if Gemini key is missing, we still report availability from SurePass
        // But suggestions will be empty
        message =
          "Name not available. (Creative suggestions could not be generated: API key missing)";
      } else {
        try {
          const genAI = new GoogleGenerativeAI(geminiApiKey);
          const model = genAI.getGenerativeModel({
            model: "gemini-2.0-flash-lite-001",
          });
          const prompt = `Generate 5 unique, creative, and brandable company names.
          The user's initial proposed name was "${name}".
          ${
            businessKeywords
              ? `The business keywords are: "${businessKeywords}".`
              : ""
          }
          The names should sound professional and be suitable for a startup.
          Do not include common legal suffixes like "Pvt Ltd" or "LLP".
          Provide them as a comma-separated list.`;

          const result = await model.generateContent(prompt);
          const responseText = result.response.text();

          finalSuggestions = responseText
            .split(",")
            .map((s) => s.trim())
            .filter((s) => s.length > 0)
            .slice(0, 5); // Limit to 5
        } catch (geminiError) {
          console.error("Error calling Gemini API:", geminiError);
          message =
            "Name not available. (Creative suggestions could not be generated: " +
            (geminiError instanceof Error
              ? geminiError.message
              : "unknown error") +
            ")";
          finalSuggestions = []; // Ensure empty array on Gemini error
        }
      }
    } else {
      // --- Name IS available (SurePass found no conflicts) ---
      message =
        "No matching companies found. The name appears to be available.";
      finalSuggestions = []; // No suggestions if name is available
    }

    return NextResponse.json({
      available: isAvailable, // True if no SurePass matches, false if matches found
      suggestions: finalSuggestions, // Contains Gemini's names ONLY if not available, else empty
      message: message, // A descriptive message
    });
  } catch (error) {
    console.error("Error in API route:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
        available: false, // Default to unavailable on server error
        suggestions: [], // No suggestions on server error
      },
      { status: 500 }
    );
  }
}
