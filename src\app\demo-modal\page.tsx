'use client';

import { useState } from 'react';
import CompanyTypeModal from '@/components/CompanyTypeModal';

interface CompanyType {
  id: string;
  name: string;
  description: string;
  minCapital: string;
  icon: string;
}

export default function DemoModalPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<CompanyType | null>(null);

  const handleCompanyTypeSelect = (companyType: CompanyType) => {
    setSelectedCompany(companyType);
    console.log('Selected company type:', companyType);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
      <div className="max-w-2xl mx-auto text-center">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Company Type Selection Modal Demo
          </h1>
          
          <p className="text-gray-600 mb-8">
            Click the button below to see the company type selection modal in action.
          </p>

          {selectedCompany && (
            <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-green-900 mb-2">
                Last Selected Company Type:
              </h3>
              <div className="flex items-center justify-center space-x-3">
                <span className="text-2xl">{selectedCompany.icon}</span>
                <div>
                  <p className="font-medium text-green-800">{selectedCompany.name}</p>
                  <p className="text-sm text-green-700">Min Capital: {selectedCompany.minCapital}</p>
                </div>
              </div>
            </div>
          )}

          <button
            onClick={() => setIsModalOpen(true)}
            className="bg-blue-600 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl"
          >
            Open Company Type Modal
          </button>

          <div className="mt-8 text-left bg-gray-50 rounded-xl p-6">
            <h3 className="font-semibold text-gray-900 mb-3">Features Demonstrated:</h3>
            <ul className="text-sm text-gray-700 space-y-2">
              <li className="flex items-center">
                <span className="text-green-600 mr-2">✓</span>
                Company type selection with visual cards
              </li>
              <li className="flex items-center">
                <span className="text-green-600 mr-2">✓</span>
                Minimum share capital display for each type
              </li>
              <li className="flex items-center">
                <span className="text-green-600 mr-2">✓</span>
                Confirmation screen with proceed option
              </li>
              <li className="flex items-center">
                <span className="text-green-600 mr-2">✓</span>
                Responsive design for mobile and desktop
              </li>
              <li className="flex items-center">
                <span className="text-green-600 mr-2">✓</span>
                Smooth animations and hover effects
              </li>
            </ul>
          </div>
        </div>
      </div>

      <CompanyTypeModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onProceed={handleCompanyTypeSelect}
      />
    </div>
  );
}
