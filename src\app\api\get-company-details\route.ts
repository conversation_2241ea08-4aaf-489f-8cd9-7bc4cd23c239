import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');

    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    // Fetch company details from basicCompanyDetailStep1 table
    const companyDetails = await prisma.basicCompanyDetailStep1.findFirst({
      where: {
        userId: user.id,
        id: registrationId // registrationId is actually the BasicCompanyDetailStep1 id
      }
    });

    if (!companyDetails) {
      return NextResponse.json(
        { error: 'Company details not found' },
        { status: 404 }
      );
    }

    // Parse share ratios from JSON field
    let shareRatios = [];
    if (companyDetails.shareRatios) {
      try {
        const ratiosData = Array.isArray(companyDetails.shareRatios)
          ? companyDetails.shareRatios
          : JSON.parse(companyDetails.shareRatios as string);
        shareRatios = ratiosData;
      } catch (error) {
        console.error('Error parsing share ratios:', error);
        shareRatios = [];
      }
    }

    // Format the response data
    const responseData = {
      // Basic Company Details
      companyName: companyDetails.companyName,
      companyType: companyDetails.companyType || 'Not specified',
      state: companyDetails.state,
      alternateName: companyDetails.alternateName,
      businessKeywords: companyDetails.businessKeywords,
      generatedObjective: companyDetails.generatedObjective,

      // Address & Capital Details
      companyAddress: `${companyDetails.state}`, // Basic address info available
      isDirectorAddressSame: companyDetails.isDirectorAddressSame,
      addressProofType: companyDetails.addressProofType,
      shareCapital: companyDetails.totalShareCapital || 0,
      shareRatios: shareRatios
    };

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Error fetching company details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
