'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import axios from 'axios';


interface AuthFormProps {
  type: 'signup' | 'login';
}

interface SignupFormData {
  firstName: string;
  middleName: string;
  lastName: string;
  identifier: string;
}

interface LoginFormData {
  identifier: string;
}

export default function AuthForm({ type }: AuthFormProps) {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const router = useRouter();

  const signupForm = useForm<SignupFormData>();
  const loginForm = useForm<LoginFormData>();

  const onSubmit = async (data: SignupFormData | LoginFormData) => {
    setLoading(true);
    setError('');
    setMessage('');
  
    try {
      let endpoint = '';
      let body = {};
  
      if (type === 'signup') {
        const signupData = data as SignupFormData;
        endpoint = '/api/auth/signup';
        body = {
          firstName: signupData.firstName,
          middleName: signupData.middleName || undefined,
          lastName: signupData.lastName || undefined,
          identifier: signupData.identifier,
        };
      } else {
        const loginData = data as LoginFormData;
        endpoint = '/api/auth/login';
        body = { identifier: loginData.identifier };
      }
  
      // ✅ Axios POST request
      const response = await axios.post(endpoint, body);
      const responseData = response.data;
  
      setMessage(responseData.message);
  
      if (responseData.otpMessage && process.env.NODE_ENV === 'development') {
        console.log('🔐 Development OTP:', responseData.otpMessage);
      }
  
      const params = new URLSearchParams({
        identifier:
          type === 'signup'
            ? (data as SignupFormData).identifier
            : (data as LoginFormData).identifier,
        type: 'user',
      });
  
      router.push(`/verify-otp?${params.toString()}`);
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        setError(err.response?.data?.error || 'Something went wrong');
      } else {
        setError('An error occurred');
      }
    } finally {
      setLoading(false);
    }
  };
  

  const getTitle = () => {
    switch (type) {
      case 'signup': return 'Sign Up';
      case 'login': return 'Login';
      default: return 'Auth';
    }
  };

  return (
    <div className="w-full p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">{getTitle()}</h2>
      
      {type === 'signup' ? (
        <form onSubmit={signupForm.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                Enter First Name *
              </label>
              <input
                type="text"
                id="firstName"
                placeholder='First name'
                {...signupForm.register('firstName', {
                  required: 'First name is required',
                  minLength: {
                    value: 2,
                    message: 'First name must be at least 2 characters'
                  }
                })}
                className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  signupForm.formState.errors.firstName ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {signupForm.formState.errors.firstName && (
                <p className="mt-1 text-sm text-red-600">{signupForm.formState.errors.firstName.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="middleName" className="block text-sm font-medium text-gray-700">
                Enter Middle Name
              </label>
              <input
                type="text"
                id="middleName"
                placeholder='Middle name'
                {...signupForm.register('middleName')}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                Enter Last Name
              </label>
              <input
                type="text"
                id="lastName"
                placeholder='Last name'
                {...signupForm.register('lastName')}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label htmlFor="identifier" className="block text-sm font-medium text-gray-700">
              Email or Contact *
            </label>
            <input
              type="text"
              id="identifier"
              placeholder="Enter email address or 10-digit phone number"
              {...signupForm.register('identifier', {
                required: 'Email or contact is required',
                validate: (value) => {
                  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
                  const phoneRegex = /^[0-9]{10}$/;
                  if (emailRegex.test(value) || phoneRegex.test(value)) {
                    return true;
                  }
                  return 'Please enter a valid email address or 10-digit phone number';
                }
              })}
              className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                signupForm.formState.errors.identifier ? 'border-red-300' : 'border-gray-300'
              }`}
            />
            {signupForm.formState.errors.identifier && (
              <p className="mt-1 text-sm text-red-600">{signupForm.formState.errors.identifier.message}</p>
            )}
          </div>

          {error && (
            <div className="text-red-600 text-sm bg-red-50 p-3 rounded">
              {error}
            </div>
          )}

          {message && (
            <div className="text-green-600 text-sm bg-green-50 p-3 rounded whitespace-pre-line">
              {message}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Processing...' : getTitle()}
          </button>
        </form>
      ) : (
        <form onSubmit={loginForm.handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="identifier" className="block text-sm font-medium text-gray-700">
              Email or Contact *
            </label>
            <input
              type="text"
              id="identifier"
              {...loginForm.register('identifier', {
                required: 'Email or contact is required'
              })}
              className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                loginForm.formState.errors.identifier ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter your email or contact number"
            />
            {loginForm.formState.errors.identifier && (
              <p className="mt-1 text-sm text-red-600">{loginForm.formState.errors.identifier.message}</p>
            )}
          </div>

          {error && (
            <div className="text-red-600 text-sm bg-red-50 p-3 rounded">
              {error}
            </div>
          )}

          {message && (
            <div className="text-green-600 text-sm bg-green-50 p-3 rounded whitespace-pre-line">
              {message}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Processing...' : getTitle()}
          </button>
        </form>
      )}

      <div className="mt-4 text-center">
        {type === 'login' && (
          <p className="text-sm text-gray-600">
            Don&apos;t have an account?{' '}
            <a href="/signup" className="text-blue-600 hover:text-blue-500">
              Sign up
            </a>
          </p>
        )}
        {type === 'signup' && (
          <p className="text-sm text-gray-600">
            Already have an account?{' '}
            <a href="/login" className="text-blue-600 hover:text-blue-500">
              Login
            </a>
          </p>
        )}
        <p className="text-sm text-gray-600 mt-2">
          <a href="/admin/login" className="text-blue-600 hover:text-blue-500">
            Admin Login
          </a>
        </p>
      </div>
    </div>
  );
}
