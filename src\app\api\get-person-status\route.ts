import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');

    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    // Get all person invitations for this registration
    const invitations = await prisma.personInvitation.findMany({
      where: {
        registrationId,
        userId: user.id
      },
      include: {
        personDetail: true
      }
    });

    // Get all person details for this registration
    const personDetails = await prisma.personDetail.findMany({
      where: {
        registrationId,
        userId: user.id
      }
    });

    // Create status map for each person index
    const personStatuses: Record<number, {
      inviteStatus: 'none' | 'pending' | 'form_submitted' | 'documents_submitted';
      detailsCompleted: boolean;
      documentsCompleted: boolean;
      hasInvitation: boolean;
      invitationId?: string;
      inviteLink?: string;
      inviteExpiry?: string;
    }> = {};

    // DO NOT initialize all persons - only add persons that actually exist in database

    // Update statuses based on invitations
    invitations.forEach(invitation => {
      const personIndex = invitation.personIndex;
      let inviteStatus: 'none' | 'pending' | 'form_submitted' | 'documents_submitted' = 'pending';

      if (invitation.status === 'FORM_SUBMITTED') {
        inviteStatus = 'form_submitted';
      } else if (invitation.status === 'DOCUMENTS_SUBMITTED') {
        inviteStatus = 'documents_submitted';
      }

      personStatuses[personIndex] = {
        inviteStatus,
        detailsCompleted: invitation.status === 'FORM_SUBMITTED' || invitation.status === 'DOCUMENTS_SUBMITTED',
        documentsCompleted: invitation.status === 'DOCUMENTS_SUBMITTED',
        hasInvitation: true,
        invitationId: invitation.id,
        inviteLink: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/invite/${invitation.inviteToken}`,
        inviteExpiry: invitation.expiresAt.toISOString()
      };
    });

    // Update statuses based on person details (for manually filled persons)
    // Only process if the person doesn't already have an invitation status
    personDetails.forEach(detail => {
      if (!detail.invitationId) { // Only for manually filled persons
        const personIndex = detail.personIndex;

        // Only set status if this person doesn't already have an invitation
        if (!personStatuses[personIndex]) {
          personStatuses[personIndex] = {
            inviteStatus: 'none',
            detailsCompleted: detail.isCompleted,
            documentsCompleted: detail.documentsCompleted,
            hasInvitation: false
          };
        }
      }
    });

    // Debug logging
    console.log('=== get-person-status API Debug ===');
    console.log('Registration ID:', registrationId);
    console.log('Person Details from DB:', personDetails.map(p => ({ personIndex: p.personIndex, invitationId: p.invitationId })));
    console.log('Invitations from DB:', invitations.map(i => ({ personIndex: i.personIndex, status: i.status })));
    console.log('Final personStatuses:', personStatuses);
    console.log('Total persons in response:', Object.keys(personStatuses).length);

    return NextResponse.json({
      success: true,
      data: {
        personStatuses,
        totalInvitations: invitations.length,
        completedInvitations: invitations.filter(inv => inv.status === 'DOCUMENTS_SUBMITTED').length
      }
    });

  } catch (error) {
    console.error('Error getting person status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
