import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixNullUserIds() {
  try {
    // Find all registrations with null userId using raw query since schema defines userId as non-nullable
    // but database might have null values due to data inconsistency
    const nullUserRegistrations = await prisma.$queryRaw<Array<{
      id: string;
      companyName: string;
      userId: string | null;
    }>>`
      SELECT id, "companyName", "userId"
      FROM "basicCompanyDetailStep1"
      WHERE "userId" IS NULL
    `;

    console.log(`Found ${nullUserRegistrations.length} registrations with null userId`);

    if (nullUserRegistrations.length > 0) {
      // Option 1: Delete them (since they're test data)
      console.log('Deleting registrations with null userId...');
      
      for (const registration of nullUserRegistrations) {
        // Delete related records first
        await prisma.personDetail.deleteMany({
          where: { registrationId: registration.id }
        });
        
        await prisma.personInvitation.deleteMany({
          where: { registrationId: registration.id }
        });
        
        // Delete the registration
        await prisma.basicCompanyDetailStep1.delete({
          where: { id: registration.id }
        });
        
        console.log(`Deleted registration: ${registration.id} (${registration.companyName})`);
      }
    }

    console.log('✅ Cleanup completed');
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixNullUserIds();
