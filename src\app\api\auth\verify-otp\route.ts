import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyOTP } from '@/lib/otp';
import { generateToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { identifier, otp } = await request.json();

    // Validation
    if (!identifier || !otp) {
      return NextResponse.json(
        { error: 'Identifier and OTP are required' },
        { status: 400 }
      );
    }

    // Verify OTP
    const otpResult = await verifyOTP(identifier, otp);

    if (!otpResult.valid || !otpResult.userId) {
      return NextResponse.json(
        { error: 'Invalid or expired OTP' },
        { status: 401 }
      );
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: otpResult.userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Generate JWT token
    const token = generateToken({
      id: user.id,
      firstName: user.firstName,
      middleName: user.middleName || undefined,
      lastName: user.lastName || undefined,
      email: user.email || undefined,
      contact: user.contact || undefined,
      role: user.role,
    });

    // Determine redirect URL based on role
    const redirectUrl = user.role === 'ADMIN' ? '/admin/dashboard' : '/';

    // Create response with token in cookie
    const response = NextResponse.json({
      message: 'OTP verified successfully',
      user: {
        id: user.id,
        firstName: user.firstName,
        middleName: user.middleName,
        lastName: user.lastName,
        email: user.email,
        contact: user.contact,
        role: user.role,
      },
      redirectUrl,
    });

    // Set HTTP-only cookie
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    });

    return response;

  } catch (error) {
    console.error('OTP verification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
