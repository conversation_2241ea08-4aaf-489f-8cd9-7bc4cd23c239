import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Types
export type RegistrationStatus = 'IN_PROGRESS' | 'SUBMITTED' | 'DOCUMENTS_PENDING' | 'COMPLETED';

export interface PersonData {
  firstName: string;
  middleName?: string;
  lastName?: string;
  email: string;
  contact: string;
  aadhaarNumber?: string;
  aadhaarVerified: boolean;
  aadhaarData?: unknown;
  panNumber?: string;
  panVerified: boolean;
  panData?: unknown;
  hasDIN?: boolean;
  dinNumber?: string;
  dinVerified: boolean;
  dinData?: unknown;

  electricityConnectionNumber?: string;
  electricityBoardName?: string;
  electricityVerified?: boolean;
  electricityData?: unknown;
  addressVerificationMethod?: string;
}

export interface CompanyType {
  id: string;
  name: string;
  code: string;
  description: string;
  minShareCapital: number;
}

export interface RegistrationState {
  // Step 1: Company Selection
  selectedCompanyType: CompanyType | null;
  companyName: string;
  alternativeName: string;
  businessKeywords: string;
  companyObjectives: string[];
  companyState: string;
  registrationId: string;

  // Step 2: Person Details
  persons: PersonData[];
  currentStep: number;

  // Step 3: Capital & Address
  shareCapital: number;
  shareRatios: { [personIndex: number]: number };
  companyAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    sameAsDirector: boolean;
  };

  // Invite Management
  inviteStatuses: Record<number, 'none' | 'pending' | 'form_submitted' | 'documents_submitted'>;
  inviteLinks: Record<number, string>;
  inviteExpiries: Record<number, string>;
  documentsCompleted: Record<number, boolean>;

  // Form State
  registrationStatus: RegistrationStatus;
  isSubmitting: boolean;
  lastSaved: string | null;
  
  // Actions
  setCompanyType: (companyType: CompanyType) => void;
  setCompanyName: (name: string) => void;
  setAlternativeName: (name: string) => void;
  setBusinessKeywords: (keywords: string) => void;
  setCompanyObjectives: (objectives: string[]) => void;
  setCompanyState: (state: string) => void;
  setRegistrationId: (id: string) => void;

  setPersons: (persons: PersonData[]) => void;
  updatePerson: (index: number, person: Partial<PersonData>) => void;
  addPerson: (person: PersonData) => void;
  removePerson: (index: number) => void;

  setCurrentStep: (step: number) => void;
  setShareCapital: (capital: number) => void;
  setShareRatios: (ratios: { [personIndex: number]: number }) => void;
  setCompanyAddress: (address: Partial<RegistrationState['companyAddress']>) => void;

  // Invite management actions
  setInviteStatus: (personIndex: number, status: 'none' | 'pending' | 'form_submitted' | 'documents_submitted') => void;
  setInviteLink: (personIndex: number, link: string) => void;
  setInviteExpiry: (personIndex: number, expiry: Date | string) => void;
  setDocumentCompleted: (personIndex: number, completed: boolean) => void;
  updateInviteData: (personIndex: number, data: { status?: string; link?: string; expiry?: Date | string; documentsCompleted?: boolean }) => void;

  setRegistrationStatus: (status: RegistrationStatus) => void;
  setIsSubmitting: (submitting: boolean) => void;
  updateLastSaved: () => void;

  // Reset functions
  resetForm: () => void;
  resetPersons: () => void;
  clearFormData: () => void;
}

const initialState = {
  selectedCompanyType: null,
  companyName: '',
  alternativeName: '',
  businessKeywords: '',
  companyObjectives: [],
  companyState: '',
  registrationId: '',
  persons: [],
  currentStep: 1,
  shareCapital: 0,
  shareRatios: {},
  companyAddress: {
    street: '',
    city: '',
    state: '',
    pincode: '',
    sameAsDirector: false,
  },
  inviteStatuses: {},
  inviteLinks: {},
  inviteExpiries: {},
  documentsCompleted: {},
  registrationStatus: 'IN_PROGRESS' as RegistrationStatus,
  isSubmitting: false,
  lastSaved: null,
};

export const useRegistrationStore = create<RegistrationState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Company actions
      setCompanyType: (companyType) => set({ selectedCompanyType: companyType }),
      setCompanyName: (name) => set({ companyName: name }),
      setAlternativeName: (name) => set({ alternativeName: name }),
      setBusinessKeywords: (keywords) => set({ businessKeywords: keywords }),
      setCompanyObjectives: (objectives) => set({ companyObjectives: objectives }),
      setCompanyState: (state) => set({ companyState: state }),
      setRegistrationId: (id) => set({ registrationId: id }),
      
      // Person actions
      setPersons: (persons) => set({ persons }),
      updatePerson: (index, personUpdate) => {
        const { persons } = get();
        const updatedPersons = [...persons];
        updatedPersons[index] = { ...updatedPersons[index], ...personUpdate };
        set({ persons: updatedPersons });
      },
      addPerson: (person) => {
        const { persons } = get();
        set({ persons: [...persons, person] });
      },
      removePerson: (index) => {
        const { persons } = get();
        const updatedPersons = persons.filter((_, i) => i !== index);
        set({ persons: updatedPersons });
      },
      
      // Step and form actions
      setCurrentStep: (step) => set({ currentStep: step }),
      setShareCapital: (capital) => set({ shareCapital: capital }),
      setShareRatios: (ratios) => set({ shareRatios: ratios }),
      setCompanyAddress: (address) => {
        const { companyAddress } = get();
        set({ companyAddress: { ...companyAddress, ...address } });
      },
      
      // Invite management actions
      setInviteStatus: (personIndex, status) => {
        const { inviteStatuses } = get();
        set({ inviteStatuses: { ...inviteStatuses, [personIndex]: status } });
      },
      setInviteLink: (personIndex, link) => {
        const { inviteLinks } = get();
        set({ inviteLinks: { ...inviteLinks, [personIndex]: link } });
      },
      setInviteExpiry: (personIndex, expiry) => {
        const { inviteExpiries } = get();
        const expiryString = expiry instanceof Date ? expiry.toISOString() : expiry;
        set({ inviteExpiries: { ...inviteExpiries, [personIndex]: expiryString } });
      },
      setDocumentCompleted: (personIndex, completed) => {
        const { documentsCompleted } = get();
        set({ documentsCompleted: { ...documentsCompleted, [personIndex]: completed } });
      },
      updateInviteData: (personIndex, data) => {
        const state = get();
        const updates: Partial<RegistrationState> = {};

        if (data.status) {
          updates.inviteStatuses = { ...state.inviteStatuses, [personIndex]: data.status as any };
        }
        if (data.link) {
          updates.inviteLinks = { ...state.inviteLinks, [personIndex]: data.link };
        }
        if (data.expiry) {
          const expiryString = data.expiry instanceof Date ? data.expiry.toISOString() : data.expiry;
          updates.inviteExpiries = { ...state.inviteExpiries, [personIndex]: expiryString };
        }
        if (data.documentsCompleted !== undefined) {
          updates.documentsCompleted = { ...state.documentsCompleted, [personIndex]: data.documentsCompleted };
        }

        set(updates);
      },

      setRegistrationStatus: (status) => set({ registrationStatus: status }),
      setIsSubmitting: (submitting) => set({ isSubmitting: submitting }),
      updateLastSaved: () => set({ lastSaved: new Date().toISOString() }),

      // Reset functions
      resetForm: () => set(initialState),
      resetPersons: () => set({ persons: [] }),
      clearFormData: () => set(initialState),
    }),
    {
      name: 'registration-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        selectedCompanyType: state.selectedCompanyType,
        companyName: state.companyName,
        alternativeName: state.alternativeName,
        businessKeywords: state.businessKeywords,
        companyObjectives: state.companyObjectives,
        companyState: state.companyState,
        registrationId: state.registrationId,
        persons: state.persons,
        currentStep: state.currentStep,
        shareCapital: state.shareCapital,
        shareRatios: state.shareRatios,
        companyAddress: state.companyAddress,
        inviteStatuses: state.inviteStatuses,
        inviteLinks: state.inviteLinks,
        inviteExpiries: state.inviteExpiries,
        documentsCompleted: state.documentsCompleted,
        lastSaved: state.lastSaved,
      }),
    }
  )
);

// Helper hooks for specific data
export const useCompanyData = () => {
  const store = useRegistrationStore();
  return {
    selectedCompanyType: store.selectedCompanyType,
    companyName: store.companyName,
    alternativeName: store.alternativeName,
    businessKeywords: store.businessKeywords,
    companyObjectives: store.companyObjectives,
    companyState: store.companyState,
    registrationId: store.registrationId,
    setCompanyType: store.setCompanyType,
    setCompanyName: store.setCompanyName,
    setAlternativeName: store.setAlternativeName,
    setBusinessKeywords: store.setBusinessKeywords,
    setCompanyObjectives: store.setCompanyObjectives,
    setCompanyState: store.setCompanyState,
    setRegistrationId: store.setRegistrationId,
  };
};

export const usePersonsData = () => {
  const store = useRegistrationStore();
  return {
    persons: store.persons,
    setPersons: store.setPersons,
    updatePerson: store.updatePerson,
    addPerson: store.addPerson,
    removePerson: store.removePerson,
  };
};

export const useFormProgress = () => {
  const store = useRegistrationStore();
  return {
    currentStep: store.currentStep,
    isSubmitting: store.isSubmitting,
    lastSaved: store.lastSaved,
    setCurrentStep: store.setCurrentStep,
    setIsSubmitting: store.setIsSubmitting,
    updateLastSaved: store.updateLastSaved,
  };
};
