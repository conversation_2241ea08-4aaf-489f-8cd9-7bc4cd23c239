import { useEffect } from 'react';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { useRegistrationStore, PersonData } from '@/store/registrationStore';

interface FormData {
  persons: PersonData[];
  companyName: string;
  companyObjectives: string[];
  companyState: string;
  shareCapital: number;
  shareRatios: { [personIndex: number]: number };
  companyAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    sameAsDirector: boolean;
  };
}

/**
 * Hook to sync React Hook Form with Zustand store
 * This ensures form data persists across page refreshes
 */
export const useFormSync = (
  setValue: UseFormSetValue<FormData>,
  watch: UseFormWatch<FormData>
) => {
  const {
    persons,
    companyName,
    companyObjectives,
    companyState,
    shareCapital,
    shareRatios,
    companyAddress,
    setPersons,
    setCompanyName,
    setCompanyObjectives,
    setCompanyState,
    setShareCapital,
    setShareRatios,
    setCompanyAddress,
    updateLastSaved,
  } = useRegistrationStore();

  // Load data from Zustand to React Hook Form on mount
  useEffect(() => {
    if (persons.length > 0) {
      setValue('persons', persons);
    }
    if (companyName) {
      setValue('companyName', companyName);
    }
    if (companyObjectives.length > 0) {
      setValue('companyObjectives', companyObjectives);
    }
    if (companyState) {
      setValue('companyState', companyState);
    }
    if (shareCapital > 0) {
      setValue('shareCapital', shareCapital);
    }
    if (Object.keys(shareRatios).length > 0) {
      setValue('shareRatios', shareRatios);
    }
    if (companyAddress.street || companyAddress.city) {
      setValue('companyAddress', companyAddress);
    }
  }, []);

  // Watch form changes and sync to Zustand
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (!name) return;

      // Sync persons data
      if (name.startsWith('persons') && value.persons) {
        // Filter out undefined values and ensure complete PersonData objects
        const validPersons = value.persons.filter((person): person is PersonData => {
          return person !== undefined && person !== null;
        });
        setPersons(validPersons);
        updateLastSaved();
      }
      
      // Sync company data
      if (name === 'companyName' && value.companyName !== undefined) {
        setCompanyName(value.companyName);
        updateLastSaved();
      }
      
      if (name === 'companyObjectives' && value.companyObjectives) {
        // Filter out undefined values
        const validObjectives = value.companyObjectives.filter((obj): obj is string => {
          return obj !== undefined && obj !== null;
        });
        setCompanyObjectives(validObjectives);
        updateLastSaved();
      }
      
      if (name === 'companyState' && value.companyState !== undefined) {
        setCompanyState(value.companyState);
        updateLastSaved();
      }
      
      // Sync capital data
      if (name === 'shareCapital' && value.shareCapital !== undefined) {
        setShareCapital(value.shareCapital);
        updateLastSaved();
      }
      
      if (name === 'shareRatios' && value.shareRatios) {
        // Filter out undefined values and ensure valid numbers
        const validShareRatios: { [personIndex: number]: number } = {};
        Object.entries(value.shareRatios).forEach(([key, val]) => {
          if (val !== undefined && val !== null && typeof val === 'number') {
            validShareRatios[parseInt(key)] = val;
          }
        });
        setShareRatios(validShareRatios);
        updateLastSaved();
      }
      
      // Sync address data
      if (name.startsWith('companyAddress') && value.companyAddress) {
        setCompanyAddress(value.companyAddress);
        updateLastSaved();
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, setPersons, setCompanyName, setCompanyObjectives, setCompanyState, 
      setShareCapital, setShareRatios, setCompanyAddress, updateLastSaved]);

  return {
    // Helper functions to manually sync specific data
    syncPersons: (persons: PersonData[]) => {
      setValue('persons', persons);
      setPersons(persons);
      updateLastSaved();
    },
    
    syncCompanyData: (data: { name?: string; objectives?: string[]; state?: string }) => {
      if (data.name !== undefined) {
        setValue('companyName', data.name);
        setCompanyName(data.name);
      }
      if (data.objectives) {
        setValue('companyObjectives', data.objectives);
        setCompanyObjectives(data.objectives);
      }
      if (data.state !== undefined) {
        setValue('companyState', data.state);
        setCompanyState(data.state);
      }
      updateLastSaved();
    },
    
    // Get current Zustand state
    getZustandState: () => ({
      persons,
      companyName,
      companyObjectives,
      companyState,
      shareCapital,
      shareRatios,
      companyAddress,
    }),
  };
};

/**
 * Hook specifically for person form sync
 */
export const usePersonFormSync = (
  personIndex: number,
  setValue: UseFormSetValue<any>,
  watch: UseFormWatch<any>
) => {
  const { persons, updatePerson, updateLastSaved } = useRegistrationStore();

  // Load person data from Zustand on mount
  useEffect(() => {
    if (persons[personIndex]) {
      const person = persons[personIndex];
      Object.keys(person).forEach((key) => {
        setValue(`persons.${personIndex}.${key}`, person[key as keyof PersonData]);
      });
    }
  }, [personIndex, setValue]);

  // Watch person changes and sync to Zustand
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (!name || !name.startsWith(`persons.${personIndex}`)) return;
      
      if (value.persons && value.persons[personIndex]) {
        updatePerson(personIndex, value.persons[personIndex]);
        updateLastSaved();
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, personIndex, updatePerson, updateLastSaved]);

  return {
    // Helper to manually update person data
    updatePersonData: (data: Partial<PersonData>) => {
      Object.keys(data).forEach((key) => {
        setValue(`persons.${personIndex}.${key}`, data[key as keyof PersonData]);
      });
      updatePerson(personIndex, data);
      updateLastSaved();
    },
    
    // Get current person data from Zustand
    getPersonData: () => persons[personIndex] || null,
  };
};
