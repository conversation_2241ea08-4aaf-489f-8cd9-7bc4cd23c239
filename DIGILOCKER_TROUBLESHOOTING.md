# DigiLocker Integration Troubleshooting Guide

## Issues Identified and Fixed

### 1. Missing DigiLocker Callback Page
**Problem**: The DigiLocker redirect URL pointed to `/digilocker-callback` but this page didn't exist.

**Solution**: Created `src/app/digilocker-callback/page.tsx` that:
- Handles the DigiLocker authentication callback
- Extracts parameters from the URL (code, client_id, error)
- Sends messages back to the parent window
- Provides user feedback and auto-closes the popup

### 2. Environment Variable Mismatch
**Problem**: The initialize-digilocker API was looking for `SUREPASS_BEARER_TOKEN` but your .env file has `SUREPASS_API_TOKEN`.

**Solution**: Updated `src/app/api/initialize-digilocker/route.ts` to use `SUREPASS_API_TOKEN` for consistency.

### 3. Incorrect DigiLocker URL Format
**Problem**: The DigiLocker URL was using an incorrect format that doesn't match Surepass documentation.

**Solution**: Updated the URL construction in `AadhaarVerification.tsx` to use:
```
https://kyc.surepass.io/api/v1/digilocker/redirect?token=${token}&client_id=${clientId}&redirect_uri=${redirectUri}
```

### 4. Popup Communication Issues
**Problem**: No proper communication between the popup and parent window.

**Solution**: 
- Added message listener in the main component to handle popup callbacks
- Implemented proper error handling and success flow
- Removed duplicate polling logic that was causing conflicts

## Testing Your Integration

### Step 1: Use the Test Page
Navigate to `/test-digilocker` in your browser to test the integration:

1. **Test Initialize**: Verifies your API can get tokens from Surepass
2. **Open Popup**: Tests the DigiLocker authentication flow
3. **Test Verify**: Checks if Aadhaar data can be retrieved

### Step 2: Check Browser Console
Monitor the browser console for:
- Network requests to your APIs
- Error messages from popup blocking
- DigiLocker callback messages

### Step 3: Verify Environment Variables
Ensure your `.env` file has:
```
SUREPASS_API_TOKEN="your_token_here"
```

## Common Issues and Solutions

### Popup Blocked
**Symptoms**: Popup doesn't open, error message about popup blocking
**Solution**: 
- Ensure user interaction triggered the popup (button click)
- Check browser popup blocker settings
- Test in incognito mode

### Authentication Fails
**Symptoms**: DigiLocker shows error page or redirects with error parameter
**Solutions**:
- Verify your Surepass account has DigiLocker access enabled
- Check if you're using sandbox vs production URLs correctly
- Ensure redirect URI is whitelisted in your Surepass dashboard

### Data Not Retrieved
**Symptoms**: Polling continues indefinitely, no Aadhaar data returned
**Solutions**:
- Check if user completed the full DigiLocker flow
- Verify the client_id is being passed correctly
- Check Surepass API logs for any errors

## API Endpoints Overview

### Initialize DigiLocker
- **Endpoint**: `POST /api/initialize-digilocker`
- **Purpose**: Gets authentication token and client_id from Surepass
- **Returns**: `{ success: true, token: "...", clientId: "..." }`

### Verify Aadhaar
- **Endpoint**: `POST /api/verify-aadhaar`
- **Purpose**: Downloads Aadhaar data using client_id
- **Input**: `{ clientId: "..." }`
- **Returns**: Aadhaar data or "Data not ready yet" message

### DigiLocker Callback
- **Endpoint**: `GET /digilocker-callback`
- **Purpose**: Handles DigiLocker authentication redirect
- **Parameters**: `code`, `client_id`, `state`, `error`

## Next Steps

1. **Test the integration** using the test page at `/test-digilocker`
2. **Check browser console** for any JavaScript errors
3. **Verify Surepass configuration** in your dashboard
4. **Test with real Aadhaar credentials** in a controlled environment

## Production Considerations

1. **Update URLs**: Change sandbox URLs to production when going live
2. **Error Handling**: Add comprehensive error handling for production use
3. **Security**: Validate all callback parameters and implement CSRF protection
4. **Logging**: Add proper logging for debugging production issues

## Support

If issues persist:
1. Check Surepass documentation for any API changes
2. Contact Surepass support with specific error messages
3. Use the test page to isolate which step is failing
4. Check network tab in browser dev tools for API response details
