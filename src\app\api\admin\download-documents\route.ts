import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import J<PERSON><PERSON><PERSON> from 'jszip';

export async function POST(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { personId, documentUrls } = await request.json();

    if (!documentUrls || documentUrls.length === 0) {
      return NextResponse.json(
        { error: 'No documents to download' },
        { status: 400 }
      );
    }

    const zip = new JSZip();

    // Download each document and add to zip
    for (const doc of documentUrls) {
      try {
        console.log(`Downloading: ${doc.name} from ${doc.url}`);
        const response = await fetch(doc.url);
        if (response.ok) {
          const buffer = await response.arrayBuffer();
          const extension = getFileExtension(doc.url);
          const fileName = `${doc.name.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;
          zip.file(fileName, buffer);
          console.log(`Added to zip: ${fileName}`);
        } else {
          console.error(`Failed to download ${doc.name}: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.error(`Error downloading ${doc.name}:`, error);
        // Continue with other files even if one fails
      }
    }

    // Generate zip file
    const zipBuffer = await zip.generateAsync({ type: 'arraybuffer' });

    return new NextResponse(zipBuffer, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="person_${personId}_documents.zip"`
      }
    });

  } catch (error) {
    console.error('Error creating document zip:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getFileExtension(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const extension = pathname.split('.').pop()?.toLowerCase();

    // Common file extensions
    const validExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx', 'txt'];

    if (extension && validExtensions.includes(extension)) {
      return extension;
    }

    // Default based on common document types
    return 'pdf';
  } catch {
    return 'pdf';
  }
}
