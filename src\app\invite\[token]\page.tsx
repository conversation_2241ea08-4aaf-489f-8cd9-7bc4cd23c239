'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import axios from 'axios';
import Navbar from '@/components/Navbar';
import Footer from '@/components/footer';
import PersonForm from '@/components/registration/PersonForm';
import ToastNotification from '@/components/ToastNotification';

interface InviteData {
  invitationId: string;
  registrationId: string;
  personRole: string;
  personIndex: number;
  inviteeEmail: string;
  inviteeName?: string;
  inviteeContact?: string;
  inviterName: string;
  inviterEmail: string;
  expiresAt: string;
  status: string;
}

interface PersonData {
  firstName: string;
  middleName?: string;
  lastName?: string;
  email: string;
  contact: string;
  aadhaarNumber?: string;
  aadhaarVerified: boolean;
  aadhaarData?: unknown;
  panNumber?: string;
  panVerified: boolean;
  panData?: unknown;
  hasDIN?: boolean;
  dinNumber?: string;
  dinVerified: boolean;
  dinData?: unknown;

  electricityConnectionNumber?: string;
  electricityBoardName?: string;
  electricityVerified?: boolean;
  electricityData?: unknown;
  addressVerificationMethod?: string;
}

interface PersonFormData {
  persons: PersonData[];
}

export default function InvitePage() {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [inviteData, setInviteData] = useState<InviteData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<'success' | 'error' | 'info'>('success');

  const { control, handleSubmit, formState: { errors }, setValue } = useForm<PersonFormData>({
    defaultValues: {
      persons: [{
        firstName: '',
        middleName: '',
        lastName: '',
        email: '',
        contact: '',
        aadhaarNumber: '',
        aadhaarVerified: false,
        aadhaarData: undefined,
        panNumber: '',
        panVerified: false,
        panData: undefined,
        hasDIN: false,
        dinNumber: '',
        dinVerified: false,
        dinData: undefined,


        addressVerificationMethod: '',
      }]
    }
  });

  const validateInvite = useCallback(async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/validate-invite?token=${token}`);

      if (response.data.success) {
        const data = response.data.data;
        setInviteData(data);

        // Check if documents are already submitted, redirect to complete page
        if (data.status === 'DOCUMENTS_SUBMITTED') {
          console.log('Documents already submitted, redirecting to complete page');
          router.push(`/invite/${token}/complete`);
          return;
        }

        // Check if form is already submitted, redirect to documents page
        if (data.status === 'FORM_SUBMITTED') {
          console.log('Form already submitted, redirecting to documents page');
          router.push(`/invite/${token}/documents`);
          return;
        }

        // Pre-fill known data
        if (data.inviteeName) {
          const nameParts = data.inviteeName.split(' ');
          setValue('persons.0.firstName', nameParts[0] || '');
          setValue('persons.0.lastName', nameParts.slice(1).join(' ') || '');
        }
        if (data.inviteeEmail) {
          setValue('persons.0.email', data.inviteeEmail);
        }
        if (data.inviteeContact) {
          setValue('persons.0.contact', data.inviteeContact);
        }
      } else {
        setError(response.data.error || 'Invalid invitation');
      }
    } catch (err: unknown) {
      console.error('Error validating invite:', err);
      const errorMessage = err instanceof Error && 'response' in err &&
        typeof err.response === 'object' && err.response !== null &&
        'data' in err.response && typeof err.response.data === 'object' &&
        err.response.data !== null && 'error' in err.response.data
        ? String(err.response.data.error)
        : 'Failed to validate invitation';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, setValue]);

  useEffect(() => {
    validateInvite();
  }, [token, validateInvite]);

  const onSubmit = async (data: PersonFormData) => {
    if (!inviteData) return;

    try {
      setSaving(true);

      // Extract the first person's data from the persons array
      const personData = data.persons[0];

      const response = await axios.post('/api/save-invited-person-details', {
        invitationId: inviteData.invitationId,
        ...personData
      });

      if (response.data.success) {
        console.log('Personal details submitted successfully, response:', response.data);
        setToastType('success');
        setToastMessage(' Personal details submitted successfully! Now please upload your documents.');
        // Redirect to document upload page after a short delay to show the toast
        setTimeout(() => {
          router.push(`/invite/${token}/documents`);
        }, 2000);
      } else {
        throw new Error(response.data.error || 'Failed to save details');
      }
    } catch (error: unknown) {
      console.error('Error saving details:', error);
      setToastType('error');
      setToastMessage('❌ Failed to save details. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Validating invitation...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="text-red-500 text-6xl mb-4">❌</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Invalid Invitation</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => router.push('/')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Go to Home
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!inviteData) return null;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />
      
      <div className="flex-1 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Complete Your Details
              </h1>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <p className="text-blue-800">
                  <strong>{inviteData.inviterName}</strong> has invited you to complete your details as <strong>{inviteData.personRole}</strong> for their company registration.
                </p>
                <p className="text-blue-600 text-sm mt-1">
                  Invitation expires on: {new Date(inviteData.expiresAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
              {/* Person Form - Single person only */}
              <PersonForm
                index={0}
                role={inviteData.personRole}
                control={control}
                errors={errors}
                setValue={setValue}
                canRemove={false}
                onRemove={() => {}}
                isFirstPerson={false}
                onShowToast={(message, type) => {
                  setToastType(type);
                  setToastMessage(message);
                }}
              />

              {/* Submit Button */}
              <div className="flex justify-center pt-6 border-t border-gray-200">
                <button
                  type="submit"
                  disabled={saving}
                  className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium shadow-lg hover:shadow-xl transition-all flex items-center"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    'Complete & Submit'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      <ToastNotification
        message={toastMessage}
        onClose={() => setToastMessage(null)}
        type={toastType}
        duration={5000}
      />

      <Footer />
    </div>
  );
}
