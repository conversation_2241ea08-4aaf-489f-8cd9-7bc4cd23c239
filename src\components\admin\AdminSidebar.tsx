'use client';

interface AdminSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  stats: {
    totalUsers: number;
    totalApplications: number;
    inProgressApplications: number;
    documentNeededApplications: number;
    completedApplications: number;
  };
}

export default function AdminSidebar({ activeSection, onSectionChange, stats }: AdminSidebarProps) {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      count: null
    },
    {
      id: 'users',
      label: 'Users',
      icon: '👥',
      count: stats.totalUsers
    },
    {
      id: 'applications',
      label: 'All Applications',
      icon: '📋',
      count: stats.totalApplications
    }
  ];

  return (
    <div className="w-64 bg-white shadow-lg h-full sticky top-0">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-900">Admin Panel</h1>
        <p className="text-sm text-gray-600">TaxLegit Management</p>
      </div>

      {/* Navigation */}
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => onSectionChange(item.id)}
                className={`w-full flex items-center justify-between px-4 py-3 rounded-lg text-left transition-colors ${
                  activeSection === item.id
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </div>
                {item.count !== null && (
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    activeSection === item.id
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {item.count}
                  </span>
                )}
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Quick Stats */}
      <div className="p-4 border-t border-gray-200 mt-auto">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Quick Overview</h3>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Total Users:</span>
            <span className="font-semibold text-blue-600">{stats.totalUsers}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Active Apps:</span>
            <span className="font-semibold text-yellow-600">{stats.inProgressApplications}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Completed:</span>
            <span className="font-semibold text-green-600">{stats.completedApplications}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
