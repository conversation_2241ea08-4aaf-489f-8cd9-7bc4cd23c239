import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Simple token verification for edge runtime
function verifyToken(token: string) {
  try {
    // For now, just check if token exists and has basic structure
    // In production, you'd want proper JWT verification
    if (!token || token.length < 10) {
      return null;
    }

    // Basic JWT structure check (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    // Decode payload (base64)
    const payload = JSON.parse(atob(parts[1]));

    // Check if token is expired
    if (payload.exp && payload.exp < Date.now() / 1000) {
      return null;
    }

    return payload;
  } catch {
    return null;
  }
}

// Add paths that don't require authentication
const publicPaths = [
  '/',
  '/login',
  '/signup',
  '/admin/login',
  '/verify-otp',
  '/invite', // Allow all invite routes
];

// API paths that don't require authentication
const publicApiPaths = [
  '/api/auth/login',
  '/api/auth/signup',
  '/api/auth/admin/login',
  '/api/auth/verify-otp',
  '/api/auth/logout',
  '/api/validate-invite',
  '/api/save-invited-person-details',
  '/api/verify-aadhaar',
  '/api/verify-pan',
  '/api/verify-din',

  '/api/verify-electricity-bill',
];

export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Check if the path is public
  if (publicPaths.includes(path) || publicApiPaths.includes(path)) {
    return NextResponse.next();
  }

  // Allow all invite routes (dynamic routes like /invite/[token])
  if (path.startsWith('/invite/')) {
    return NextResponse.next();
  }

  // Get token from cookie
  const token = request.cookies.get('auth-token')?.value;

  // If no token, redirect to login
  if (!token) {
    if (path.startsWith('/admin')) {
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Verify token
  const decoded = verifyToken(token);

  if (!decoded) {
    // If token is invalid, redirect to login
    if (path.startsWith('/admin')) {
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Check admin routes - only admins can access /admin/*
  if (path.startsWith('/admin') && decoded.role !== 'ADMIN') {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Check user dashboard - only regular users can access /dashboard
  if (path === '/dashboard' && decoded.role !== 'USER') {
    return NextResponse.redirect(new URL('/admin/dashboard', request.url));
  }

  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)',
  ],
};
