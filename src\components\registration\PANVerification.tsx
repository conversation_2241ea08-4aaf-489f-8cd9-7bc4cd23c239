"use client";

import { useState } from "react";
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  useWatch,
} from "react-hook-form";
import axios from "axios";

interface PersonFormData {
  persons: Array<{
    firstName: string;
    middleName?: string;
    lastName?: string;
    email: string;
    contact: string;
    aadhaarNumber?: string;
    aadhaarVerified: boolean;
    aadhaarData?: unknown;
    panNumber?: string;
    panVerified: boolean;
    panData?:
      | {
          // Refined type for panData based on current understanding
          name: string;
          dateOfBirth: string; // From SurePass 'dob'
          panNumber: string; // From SurePass 'pan_number'
          aadhaarLinked: boolean; // From SurePass 'aadhaar_linked'
          // status?: string; // Optional: If you decide to derive and display a status
        }
      | unknown; // Allow unknown if more fields come or for manual entry
    hasDIN?: boolean;
    dinNumber?: string;
    dinVerified: boolean;
    dinData?: unknown;

    electricityConnectionNumber?: string;
    electricityBoardName?: string;
    electricityVerified?: boolean;
    electricityData?: unknown;
    addressVerificationMethod?: string;
  }>;
}

interface PANVerificationProps {
  index: number;
  control: Control<PersonFormData>;
  errors: FieldErrors<PersonFormData>;
  setValue: UseFormSetValue<PersonFormData>;
  onShowToast?: (message: string, type: "success" | "error" | "info") => void;
}

export default function PANVerification({
  index,
  control,
  errors,
  setValue,
  onShowToast,
}: PANVerificationProps) {
  const [verifying, setVerifying] = useState(false);
  // UPDATED: panData state interface - removed 'status' if not needed, confirmed 'aadhaarLinked'
  const [panData, setPanData] = useState<{
    name: string;
    dateOfBirth: string; // Corresponds to SurePass 'dob'
    panNumber: string;
    aadhaarLinked: boolean;
    // status?: string; // Make this optional or remove if you're not deriving it
  } | null>(null);
  const [showUpload, setShowUpload] = useState(false);
  const [manualPanNumber, setManualPanNumber] = useState("");

  // Watch form values using React Hook Form
  const verified = useWatch({
    control,
    name: `persons.${index}.panVerified`,
    defaultValue: false,
  });
  const panNumber = useWatch({
    control,
    name: `persons.${index}.panNumber`,
    defaultValue: "",
  });

  const handleVerifyPAN = async () => {
    if (!panNumber || panNumber.length !== 10) {
      onShowToast?.("Please enter a valid 10-character PAN number", "error");
      return;
    }

    try {
      setVerifying(true);

      const response = await axios.post("/api/verify-pan", {
        panNumber: panNumber.toUpperCase(),
        personIndex: index,
      });

      if (response.data.success) {
        const verifiedData = response.data.data;
        setPanData(verifiedData); // This sets the state for display

        // Update form state with verified data
        setValue(`persons.${index}.panVerified`, true);
        setValue(`persons.${index}.panData`, verifiedData); // This saves data to the form store

        onShowToast?.(" PAN verified successfully!", "success");
      } else {
        throw new Error(response.data.error || "PAN verification failed");
      }
    } catch (error) {
      console.error("PAN verification error:", error);
      onShowToast?.(
        "Failed to verify PAN. Please try again or enter PAN manually.",
        "error"
      );
      setShowUpload(true);
    } finally {
      setVerifying(false);
    }
  };

  const getStatusBadge = () => {
    if (verified) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          ✅ Verified
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        ❌ Not Verified
      </span>
    );
  };

  const formatPAN = (value: string) => {
    // Format PAN as **********
    return value
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, "")
      .slice(0, 10);
  };

  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <span className="font-medium text-gray-900">🏛️ PAN Verification</span>
          {getStatusBadge()}
        </div>
      </div>

      <div className="p-4 border-t border-gray-200">
        {!verified && !showUpload && (
          <div className="space-y-4">
            <div className="flex space-x-3">
              <input
                type="text"
                {...control.register(`persons.${index}.panNumber`, {
                  required: "PAN number is required",
                  minLength: {
                    value: 10,
                    message: "PAN must be 10 characters",
                  },
                  maxLength: {
                    value: 10,
                    message: "PAN must be 10 characters",
                  },
                  pattern: {
                    value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                    message: "Invalid PAN format",
                  },
                })}
                placeholder="Enter PAN number (e.g., **********)"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 uppercase"
                maxLength={10}
              />
              {errors.persons?.[index]?.panNumber && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.persons[index].panNumber.message}
                </p>
              )}
              <button
                type="button"
                onClick={handleVerifyPAN}
                disabled={verifying || !panNumber || panNumber.length !== 10}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center"
              >
                {verifying ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Verifying...
                  </>
                ) : (
                  "Verify"
                )}
              </button>
            </div>
            <button
              type="button"
              onClick={() => setShowUpload(true)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              Enter PAN manually instead
            </button>
          </div>
        )}

        {verified && panData && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-medium text-green-900 mb-3">
              Verified PAN Details:
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div>
                <strong>Name:</strong> {panData.name}
              </div>
              {/* UPDATED: Display Aadhaar Linked status */}
              <div>
                <strong>Aadhaar Linked:</strong>{" "}
                {panData.aadhaarLinked ? "Yes ✅" : "No ❌"}
              </div>
              <div>
                <strong>Date of Birth:</strong> {panData.dateOfBirth}
              </div>
              <div>
                <strong>PAN Number:</strong> {panData.panNumber}
              </div>
              {/* Removed explicit status display if API doesn't provide it directly */}
              {/* If you need a status here, you'd need to derive it in the backend
                  or from the top-level SurePass response properties (success, message) */}
            </div>
          </div>
        )}

        {showUpload && (
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-2">
                Enter PAN Number Manually
              </h4>
              <p className="text-yellow-800 text-sm mb-3">
                Enter your PAN number below. You can upload the PAN card
                document later during the document submission process.
              </p>

              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Enter 10-character PAN number (e.g., **********)"
                  maxLength={10}
                  value={manualPanNumber}
                  onChange={(e) => {
                    const value = e.target.value
                      .replace(/[^A-Z0-9]/g, "")
                      .toUpperCase(); // Only allow alphanumeric, convert to uppercase
                    setManualPanNumber(value);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />

                <button
                  type="button"
                  onClick={() => {
                    // PAN format validation: 5 letters + 4 digits + 1 letter
                    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
                    if (panRegex.test(manualPanNumber)) {
                      // Save to form data using React Hook Form
                      setValue(`persons.${index}.panNumber`, manualPanNumber);
                      setValue(`persons.${index}.panVerified`, true);
                      // When manually entering, panData will be null, or you can set a default structure
                      // For now, if manually entered, panData won't be set until API is called.
                      // If you want to store manual data in panData, you'd do something like:
                      // setValue(`persons.${index}.panData`, { panNumber: manualPanNumber, name: 'Manual Entry', dateOfBirth: 'N/A', aadhaarLinked: false, status: 'Manual' });
                      setShowUpload(false);
                      setManualPanNumber("");
                    } else {
                      onShowToast?.(
                        "Please enter a valid PAN number (format: **********)",
                        "error"
                      );
                    }
                  }}
                  disabled={manualPanNumber.length !== 10}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  Save PAN Number
                </button>
              </div>

              {/* Back to verification button */}
              <div className="mt-3">
                <button
                  type="button"
                  onClick={() => setShowUpload(false)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  ← Back to verification
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
