-- CreateEnum
CREATE TYPE "RegistrationStatus" AS ENUM ('IN_PROGRESS', 'SUBMITTED');

-- CreateEnum
CREATE TYPE "InviteStatus" AS ENUM ('PENDING', 'FORM_SUBMITTED', 'DOCUMENTS_SUBMITTED', 'EXPIRED', 'CANCELLED');

-- AlterTable
ALTER TABLE "basicCompanyDetailStep1" ADD COLUMN     "addressProofType" TEXT,
ADD COLUMN     "isDirectorAddressSame" BOOLEAN,
ADD COLUMN     "shareRatios" JSONB,
ADD COLUMN     "status" "RegistrationStatus" NOT NULL DEFAULT 'IN_PROGRESS',
ADD COLUMN     "totalShareCapital" INTEGER;

-- CreateTable
CREATE TABLE "PersonInvitation" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "registrationId" TEXT NOT NULL,
    "inviteToken" TEXT NOT NULL,
    "personRole" TEXT NOT NULL,
    "personIndex" INTEGER NOT NULL,
    "status" "InviteStatus" NOT NULL DEFAULT 'PENDING',
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PersonInvitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PersonDetail" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "invitationId" TEXT,
    "registrationId" TEXT NOT NULL,
    "personIndex" INTEGER NOT NULL,
    "personRole" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "middleName" TEXT,
    "lastName" TEXT,
    "email" TEXT NOT NULL,
    "contact" TEXT NOT NULL,
    "aadhaarVerified" BOOLEAN NOT NULL DEFAULT false,
    "aadhaarData" JSONB,
    "panVerified" BOOLEAN NOT NULL DEFAULT false,
    "panData" JSONB,
    "hasDIN" BOOLEAN NOT NULL DEFAULT false,
    "dinVerified" BOOLEAN NOT NULL DEFAULT false,
    "dinData" JSONB,
    "utilityBillVerified" BOOLEAN NOT NULL DEFAULT false,
    "utilityBillData" JSONB,
    "bankVerified" BOOLEAN NOT NULL DEFAULT false,
    "bankData" JSONB,
    "aadhaarDocUploaded" BOOLEAN NOT NULL DEFAULT false,
    "aadhaarDocUrl" TEXT,
    "panDocUploaded" BOOLEAN NOT NULL DEFAULT false,
    "panDocUrl" TEXT,
    "photoUploaded" BOOLEAN NOT NULL DEFAULT false,
    "photoUrl" TEXT,
    "signatureUploaded" BOOLEAN NOT NULL DEFAULT false,
    "signatureUrl" TEXT,
    "utilityDocUploaded" BOOLEAN NOT NULL DEFAULT false,
    "utilityDocUrl" TEXT,
    "sharePercentage" DOUBLE PRECISION,
    "isCompleted" BOOLEAN NOT NULL DEFAULT false,
    "documentsCompleted" BOOLEAN NOT NULL DEFAULT false,
    "completedAt" TIMESTAMP(3),
    "documentsCompletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PersonDetail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PersonInvitation_inviteToken_key" ON "PersonInvitation"("inviteToken");

-- CreateIndex
CREATE INDEX "PersonInvitation_userId_idx" ON "PersonInvitation"("userId");

-- CreateIndex
CREATE INDEX "PersonInvitation_inviteToken_idx" ON "PersonInvitation"("inviteToken");

-- CreateIndex
CREATE INDEX "PersonInvitation_registrationId_idx" ON "PersonInvitation"("registrationId");

-- CreateIndex
CREATE UNIQUE INDEX "PersonDetail_invitationId_key" ON "PersonDetail"("invitationId");

-- CreateIndex
CREATE INDEX "PersonDetail_userId_idx" ON "PersonDetail"("userId");

-- CreateIndex
CREATE INDEX "PersonDetail_registrationId_idx" ON "PersonDetail"("registrationId");

-- CreateIndex
CREATE INDEX "PersonDetail_invitationId_idx" ON "PersonDetail"("invitationId");

-- AddForeignKey
ALTER TABLE "PersonInvitation" ADD CONSTRAINT "PersonInvitation_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonDetail" ADD CONSTRAINT "PersonDetail_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonDetail" ADD CONSTRAINT "PersonDetail_invitationId_fkey" FOREIGN KEY ("invitationId") REFERENCES "PersonInvitation"("id") ON DELETE SET NULL ON UPDATE CASCADE;
