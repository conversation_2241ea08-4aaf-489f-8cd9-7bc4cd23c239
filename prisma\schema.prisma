generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
}

enum RegistrationStatus {
  IN_PROGRESS
  SUBMITTED
  DOCUMENTS_PENDING
  COMPLETED
}

model User {
  id         String   @id @default(cuid())
  firstName  String
  middleName String?
  lastName   String?
  email      String?  @unique
  contact    String?  @unique
  password   String?  // For admin users only
  role       Role     @default(USER)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  otps                        Otp[]
  basicCompanyDetailStep1     BasicCompanyDetailStep1[]
  personInvitations           PersonInvitation[]
  personDetails               PersonDetail[]
}

model Otp {
  id        String   @id @default(cuid())
  code      String
  userId    String
  createdAt DateTime @default(now())
  expiresAt DateTime

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model BasicCompanyDetailStep1 {
  id                  String   @id @default(cuid())
  userId              String
  firstName           String
  middleName          String?
  lastName            String?
  email               String?
  phone               String?
  state               String
  companyName         String
  alternateName       String?
  businessKeywords    String
  generatedObjective  String?
  companyType         String?

  // Address & Capital Information (Step 3)
  isDirectorAddressSame Boolean?
  addressProofType    String?
  addressProofUrl     String?
  totalShareCapital   Int?
  shareRatios         Json?

  // Registration Status
  status              RegistrationStatus @default(IN_PROGRESS)

  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("basicCompanyDetailStep1")
}

model PersonInvitation {
  id                String   @id @default(cuid())
  userId            String   // Person A who sent the invite
  registrationId    String   // Links to the registration instance
  inviteToken       String   @unique // Secure token for the invite link
  personRole        String   // Role like "Director 2", "Partner 2", etc.
  personIndex       Int      // Index in the persons array
  status            InviteStatus @default(PENDING)
  expiresAt         DateTime
  completedAt       DateTime?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user              User @relation(fields: [userId], references: [id], onDelete: Cascade)
  personDetail      PersonDetail?

  @@index([userId])
  @@index([inviteToken])
  @@index([registrationId])
}

model PersonDetail {
  id                  String   @id @default(cuid())
  userId              String   // Original user (Person A)
  invitationId        String?  @unique // If filled by invited person
  registrationId      String   // Links to the registration instance
  personIndex         Int      // Index in the persons array
  personRole          String   // Role like "Director 1", "Owner", etc.

  // Basic Information
  firstName           String
  middleName          String?
  lastName            String?
  email               String
  contact             String

  // Verification Status
  aadhaarVerified     Boolean  @default(false)
  aadhaarData         Json?

  panVerified         Boolean  @default(false)
  panData             Json?

  hasDIN              Boolean  @default(false)
  dinVerified         Boolean  @default(false)
  dinData             Json?



  electricityConnectionNumber String?
  electricityBoardName        String?
  electricityVerified         Boolean  @default(false)
  electricityData             Json?

  // Document Upload Status
  aadhaarDocUploaded  Boolean  @default(false)
  aadhaarDocUrl       String?
  panDocUploaded      Boolean  @default(false)
  panDocUrl           String?
  photoUploaded       Boolean  @default(false)
  photoUrl            String?
  signatureUploaded   Boolean  @default(false)
  signatureUrl        String?

  // New document fields
  geoTagImageUploaded         Boolean  @default(false)
  geoTagImageUrl              String?
  bankTransactionDetailsUploaded Boolean  @default(false)
  bankTransactionDetailsUrl   String?
  addressVerificationDocUploaded Boolean  @default(false)
  addressVerificationDocUrl   String?


  // Share Capital
  sharePercentage     Float?

  // Status
  isCompleted         Boolean  @default(false)
  documentsCompleted  Boolean  @default(false)
  completedAt         DateTime?
  documentsCompletedAt DateTime?
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  user                User @relation(fields: [userId], references: [id], onDelete: Cascade)
  invitation          PersonInvitation? @relation(fields: [invitationId], references: [id])

  @@index([userId])
  @@index([registrationId])
  @@index([invitationId])
}

enum InviteStatus {
  PENDING
  FORM_SUBMITTED
  DOCUMENTS_SUBMITTED
  EXPIRED
  CANCELLED
}


