import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const applicationId = id;

    // Get company details
    const companyDetail = await prisma.basicCompanyDetailStep1.findUnique({
      where: {
        id: applicationId
      },
      include: {
        user: {
          select: {
            firstName: true,
            middleName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      }
    });

    if (!companyDetail) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Get person details for this registration
    const personDetails = await prisma.personDetail.findMany({
      where: {
        registrationId: applicationId
      },
      orderBy: {
        personIndex: 'asc'
      }
    });

    return NextResponse.json({
      success: true,
      companyDetail,
      personDetails
    });

  } catch (error) {
    console.error('Error fetching application detail:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
