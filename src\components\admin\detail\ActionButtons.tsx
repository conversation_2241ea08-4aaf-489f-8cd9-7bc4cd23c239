'use client';

interface CompanyDetail {
  id: string;
  companyName: string;
  status: string;
  [key: string]: any;
}

interface PersonDetail {
  id: string;
  firstName: string;
  lastName?: string;
  [key: string]: any;
}

interface ActionButtonsProps {
  applicationId: string;
  companyDetail: CompanyDetail;
  personDetails: PersonDetail[];
}

export default function ActionButtons({ 
  applicationId, 
  companyDetail, 
  personDetails 
}: ActionButtonsProps) {

  const handleDownloadPDF = async () => {
    try {
      const response = await fetch('/api/admin/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          applicationId
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${companyDetail.companyName}_application_details.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Failed to generate PDF');
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF');
    }
  };

  const handleDownloadAllDocuments = async () => {
    try {
      // Collect all document URLs from all persons
      const allDocumentUrls: { name: string; url: string; personName: string }[] = [];
      
      personDetails.forEach(person => {
        const personName = `${person.firstName}_${person.lastName || ''}`.trim();
        
        const documents = [
          { name: 'Aadhaar_Document', url: person.aadhaarDocUrl },
          { name: 'PAN_Document', url: person.panDocUrl },
          { name: 'Photo', url: person.photoUrl },
          { name: 'Signature', url: person.signatureUrl },
          { name: 'Geo_Tag_Image', url: person.geoTagImageUrl },
          { name: 'Bank_Transaction_Details', url: person.bankTransactionDetailsUrl },
          { name: 'Address_Verification_Document', url: person.addressVerificationDocUrl }
        ];

        documents.forEach(doc => {
          if (doc.url) {
            allDocumentUrls.push({
              name: `${personName}_${doc.name}`,
              url: doc.url,
              personName: personName
            });
          }
        });
      });

      // Add company address proof if available
      if (companyDetail.addressProofUrl) {
        allDocumentUrls.push({
          name: 'Company_Address_Proof',
          url: companyDetail.addressProofUrl,
          personName: 'Company'
        });
      }

      console.log('All document URLs collected:', allDocumentUrls);

      if (allDocumentUrls.length === 0) {
        alert('No documents available for download');
        return;
      }

      const response = await fetch('/api/admin/download-all-documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          applicationId,
          companyName: companyDetail.companyName,
          documentUrls: allDocumentUrls
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${companyDetail.companyName}_all_documents.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Failed to download documents');
      }
    } catch (error) {
      console.error('Error downloading documents:', error);
      alert('Error downloading documents');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow mb-8 p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Actions</h2>
      
      <div className="flex flex-wrap gap-4">
        <button
          onClick={handleDownloadPDF}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <span>📄</span>
          <span>Download Details as PDF</span>
        </button>

        <button
          onClick={handleDownloadAllDocuments}
          className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
        >
          <span>📦</span>
          <span>Download All Documents as ZIP</span>
        </button>

        <button
          onClick={() => window.print()}
          className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
        >
          <span>🖨️</span>
          <span>Print Page</span>
        </button>
      </div>

      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">Quick Stats</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Persons:</span>
            <span className="ml-2 font-semibold">{personDetails.length}</span>
          </div>
          <div>
            <span className="text-gray-500">Completed Persons:</span>
            <span className="ml-2 font-semibold">
              {personDetails.filter(p => p.isCompleted).length}
            </span>
          </div>
          <div>
            <span className="text-gray-500">Documents Complete:</span>
            <span className="ml-2 font-semibold">
              {personDetails.filter(p => p.documentsCompleted).length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
