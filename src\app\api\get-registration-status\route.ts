import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');

    // Validation
    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    // Find the registration record
    const registration = await prisma.basicCompanyDetailStep1.findFirst({
      where: {
        id: registrationId,
        userId: user.id
      },
      select: {
        id: true,
        status: true,
        companyName: true,
        companyType: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!registration) {
      return NextResponse.json(
        { error: 'Registration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      status: registration.status,
      data: {
        id: registration.id,
        status: registration.status,
        companyName: registration.companyName,
        companyType: registration.companyType,
        createdAt: registration.createdAt,
        updatedAt: registration.updatedAt,
        isSubmitted: registration.status === 'SUBMITTED'
      }
    });

  } catch (error) {
    console.error('Error getting registration status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
