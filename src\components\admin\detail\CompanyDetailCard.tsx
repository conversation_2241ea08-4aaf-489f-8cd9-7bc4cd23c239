'use client';

interface CompanyDetail {
  id: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  state: string;
  companyName: string;
  alternateName?: string;
  businessKeywords: string;
  generatedObjective?: string;
  companyType?: string;
  isDirectorAddressSame?: boolean;
  addressProofType?: string;
  addressProofUrl?: string;
  totalShareCapital?: number;
  shareRatios?: any;
  status: string;
  createdAt: string;
  updatedAt: string;
  user: {
    firstName: string;
    middleName?: string;
    lastName?: string;
    email?: string;
    contact?: string;
  };
}

interface CompanyDetailCardProps {
  companyDetail: CompanyDetail;
}

export default function CompanyDetailCard({ companyDetail }: CompanyDetailCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUBMITTED':
      case 'DOCUMENTS_PENDING':
        return 'bg-orange-100 text-orange-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return 'In Progress';
      case 'SUBMITTED':
        return 'Document Needed';
      case 'DOCUMENTS_PENDING':
        return 'Document Needed';
      case 'COMPLETED':
        return 'Completed';
      default:
        return status;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Company Information</h2>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(companyDetail.status)}`}>
            {getStatusText(companyDetail.status)}
          </span>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Basic Company Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Company Name</label>
              <p className="text-gray-900 font-medium">{companyDetail.companyName}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Company Type</label>
              <p className="text-gray-900">{companyDetail.companyType || 'Not specified'}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Alternate Name</label>
              <p className="text-gray-900">{companyDetail.alternateName || 'Not provided'}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">State</label>
              <p className="text-gray-900">{companyDetail.state}</p>
            </div>
          </div>

          {/* Applicant Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Applicant Information</h3>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Name</label>
              <p className="text-gray-900 font-medium">
                {companyDetail.firstName} {companyDetail.middleName} {companyDetail.lastName}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-gray-900">{companyDetail.email || 'Not provided'}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Phone</label>
              <p className="text-gray-900">{companyDetail.phone || 'Not provided'}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">User Account</label>
              <p className="text-gray-900">
                {companyDetail.user.firstName} {companyDetail.user.middleName} {companyDetail.user.lastName}
              </p>
              <p className="text-sm text-gray-500">{companyDetail.user.email}</p>
            </div>
          </div>

          {/* Business Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Business Details</h3>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Business Keywords</label>
              <p className="text-gray-900">{companyDetail.businessKeywords}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Share Capital</label>
              <p className="text-gray-900 font-medium">
                {companyDetail.totalShareCapital ? `₹${companyDetail.totalShareCapital.toLocaleString()}` : 'Not specified'}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">Director Address Same</label>
              <p className="text-gray-900">
                {companyDetail.isDirectorAddressSame === true ? '✅ Yes' : 
                 companyDetail.isDirectorAddressSame === false ? '❌ No' : 'Not specified'}
              </p>
            </div>

            {companyDetail.addressProofType && (
              <div>
                <label className="text-sm font-medium text-gray-500">Address Proof Type</label>
                <p className="text-gray-900">{companyDetail.addressProofType}</p>
                {companyDetail.addressProofUrl && (
                  <a 
                    href={companyDetail.addressProofUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    📄 View Document
                  </a>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Generated Objective */}
        {companyDetail.generatedObjective && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Generated Business Objective</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-gray-700 leading-relaxed">{companyDetail.generatedObjective}</p>
            </div>
          </div>
        )}

        {/* Share Ratios */}
        {companyDetail.shareRatios && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Share Distribution</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                {JSON.stringify(companyDetail.shareRatios, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Created At</label>
              <p className="text-gray-900">{new Date(companyDetail.createdAt).toLocaleString()}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-gray-900">{new Date(companyDetail.updatedAt).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
