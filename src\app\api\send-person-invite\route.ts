import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';
// import { sendEmail, generateInviteEmailTemplate } from '@/lib/email';
// import { sendWhatsAppMessage, generateWhatsAppInviteMessage, formatPhoneForWhatsApp } from '@/lib/whatsapp';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      registrationId,
      personRole,
      personIndex
    } = await request.json();

    // Validation
    if (!registrationId || !personRole || personIndex === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate secure token
    const inviteToken = crypto.randomBytes(32).toString('hex');

    // Generate invite link (will be updated with actual token after invitation is processed)
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    // Check if an invitation already exists for this person
    const existingInvitation = await prisma.personInvitation.findFirst({
      where: {
        userId: user.id,
        registrationId,
        personIndex
      }
    });

    let invitation;

    if (existingInvitation) {
      // Check if existing invitation is expired or completed
      const now = new Date();
      const isExpired = existingInvitation.expiresAt < now;
      const isCompleted = existingInvitation.status === 'DOCUMENTS_SUBMITTED';

      if (isExpired || isCompleted) {
        // Create new invitation if expired or completed
        invitation = await prisma.personInvitation.create({
          data: {
            userId: user.id,
            registrationId,
            inviteToken,
            personRole,
            personIndex,
            status: 'PENDING',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
          }
        });
      } else {
        // Return existing invitation if still valid and not completed
        invitation = existingInvitation;
      }
    } else {
      // Create new invitation
      invitation = await prisma.personInvitation.create({
        data: {
          userId: user.id,
          registrationId,
          personRole,
          personIndex,
          inviteToken,
          status: 'PENDING',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
        }
      });
    }

    // Generate final invite link with actual token
    const inviteLink = `${baseUrl}/invite/${invitation.inviteToken}`;

    return NextResponse.json({
      success: true,
      message: 'Invitation link generated successfully',
      data: {
        inviteLink,
        token: invitation.inviteToken,
        registrationId,
        personRole,
        personIndex,
        invitationId: invitation.id,
        status: invitation.status.toLowerCase(), // Convert PENDING to pending for frontend
        expiresAt: invitation.expiresAt
      }
    });

  } catch (error) {
    console.error('Error sending invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get invitation status
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registrationId');

    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    // Get all invitations for this registration
    const invitations = await prisma.personInvitation.findMany({
      where: {
        userId: user.id,
        registrationId
      },
      orderBy: {
        personIndex: 'asc'
      }
    });

    // Add invite links to the response
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const invitationsWithLinks = invitations.map(invitation => ({
      ...invitation,
      inviteLink: `${baseUrl}/invite/${invitation.inviteToken}`
    }));

    return NextResponse.json({
      success: true,
      data: invitationsWithLinks
    });

  } catch (error) {
    console.error('Error fetching invitations:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
