'use client';

import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import axios from 'axios';

interface OTPFormProps {
  identifier: string;
  userType: 'user' | 'admin';
}

interface OTPFormData {
  otp: string;
}

export default function OTPForm({ identifier, userType }: OTPFormProps) {
  const { register, handleSubmit, formState: { errors } } = useForm<OTPFormData>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const onSubmit = async (data: OTPFormData) => {
    setLoading(true);
    setError('');

    try {
      const response = await axios.post('/api/auth/verify-otp', {
        identifier,
        otp: data.otp,
      });

      const result = response.data;

      // OTP verified successfully, redirect to appropriate page
      router.push(result.redirectUrl || (userType === 'admin' ? '/admin/dashboard' : '/'));
    } catch (err) {
      const errorMsg =
        axios.isAxiosError(err) && err.response?.data?.error
          ? err.response.data.error
          : 'Invalid OTP';
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setLoading(true);
    setError('');

    try {
      const endpoint = userType === 'admin' ? '/api/auth/admin/login' : '/api/auth/login';

      const response = await axios.post(endpoint, { identifier });
      const result = response.data;

      alert('OTP resent successfully!');
      if (result.otpMessage && process.env.NODE_ENV === 'development') {
        console.log('🔐 Development OTP (Resent):', result.otpMessage);
      }
    } catch (err) {
      const errorMsg =
        axios.isAxiosError(err) && err.response?.data?.error
          ? err.response.data.error
          : 'Failed to resend OTP';
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Verify OTP</h2>

      <div className="mb-4 p-3 bg-blue-50 rounded">
        <p className="text-sm text-blue-800">
          OTP sent to: <strong>{identifier}</strong>
        </p>
        <p className="text-xs text-blue-600 mt-1">
          Enter the 4-digit code to continue
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label htmlFor="otp" className="block text-sm font-medium text-gray-700">
            OTP Code *
          </label>
          <input
            id="otp"
            type="text"
            maxLength={4}
            inputMode="numeric"
            {...register('otp', {
              required: 'OTP is required',
              pattern: {
                value: /^[0-9]{4}$/,
                message: 'Enter a valid 4-digit OTP',
              },
            })}
            className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm text-center text-2xl tracking-widest focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
              errors.otp ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="0000"
          />
          {errors.otp && (
            <p className="mt-1 text-sm text-red-600">{errors.otp.message}</p>
          )}
        </div>

        {error && (
          <div className="text-red-600 text-sm bg-red-50 p-3 rounded">
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {loading ? 'Verifying...' : 'Verify OTP'}
        </button>
      </form>

      <div className="mt-4 text-center">
        <button
          onClick={handleResendOTP}
          disabled={loading}
          className="text-sm text-blue-600 hover:text-blue-500 disabled:opacity-50"
        >
          Resend OTP
        </button>
      </div>

      <div className="mt-4 text-center">
        <a href="/login" className="text-sm text-gray-600 hover:text-gray-500">
          ← Back to Login
        </a>
      </div>
    </div>
  );
}
