'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import AdminSidebar from '@/components/admin/AdminSidebar';
import UsersSection from '@/components/admin/UsersSection';
import ApplicationsSection from '@/components/admin/ApplicationsSection';

interface DashboardStats {
  totalUsers: number;
  totalApplications: number;
  inProgressApplications: number;
  documentNeededApplications: number;
  completedApplications: number;
}

export default function AdminDashboard() {
  const [activeSection, setActiveSection] = useState('dashboard');
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalApplications: 0,
    inProgressApplications: 0,
    documentNeededApplications: 0,
    completedApplications: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await axios.get('/api/admin/dashboard-stats');
        setStats(response.data);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const renderContent = () => {
    switch (activeSection) {
      case 'users':
        return <UsersSection />;
      case 'applications':
        return <ApplicationsSection />;
      default:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Dashboard Overview</h2>
              <p className="text-gray-600">Welcome to the admin dashboard</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Users</h3>
                <p className="text-3xl font-bold text-blue-600">
                  {loading ? '...' : stats.totalUsers}
                </p>
                <p className="text-sm text-gray-500">Registered users</p>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">All Applications</h3>
                <p className="text-3xl font-bold text-green-600">
                  {loading ? '...' : stats.totalApplications}
                </p>
                <p className="text-sm text-gray-500">Total applications</p>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">In Progress</h3>
                <p className="text-3xl font-bold text-yellow-600">
                  {loading ? '...' : stats.inProgressApplications}
                </p>
                <p className="text-sm text-gray-500">Active applications</p>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Completed</h3>
                <p className="text-3xl font-bold text-purple-600">
                  {loading ? '...' : stats.completedApplications}
                </p>
                <p className="text-sm text-gray-500">Finished applications</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Document Needed:</span>
                    <span className="font-semibold text-orange-600">
                      {loading ? '...' : stats.documentNeededApplications}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Completion Rate:</span>
                    <span className="font-semibold text-green-600">
                      {loading ? '...' : stats.totalApplications > 0
                        ? `${Math.round((stats.completedApplications / stats.totalApplications) * 100)}%`
                        : '0%'
                      }
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Status</h3>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">In Progress</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">Document Needed</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm text-gray-600">Completed</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        stats={stats}
      />
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}
