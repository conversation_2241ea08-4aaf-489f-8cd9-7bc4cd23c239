'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import axios from 'axios';
import { useRegistrationStore } from '@/store/registrationStore';

interface CompanyType {
  id: string;
  name: string;
  code: string;
  description: string;
  minShareCapital: number;
}

interface PersonData {
  firstName: string;
  lastName?: string;
  personRole: string;
  personIndex: number;
}

interface ShareRatio {
  personIndex: number;
  personName: string;
  personRole: string;
  sharePercentage: number;
}

interface CompanyAddressCapitalFormData {
  // Address Section
  isDirectorAddressSame: boolean;
  addressProofType?: string;
  addressProofFile?: FileList;
  
  // Share Capital Section
  totalShareCapital: number;
  shareRatios: ShareRatio[];
}

interface AddressCapitalFormProps {
  selectedCompanyType: CompanyType | null;
  registrationId: string;
  onSubmitSuccess: () => void;
  onPrevious: () => void;
  onShowToast?: (message: string, type: 'success' | 'error' | 'info') => void;
}

const addressProofOptions = [
  'Last 2 Month Gas Bill',
  'Last 2 Month WiFi Bill', 
  'Last 2 Month Electricity Bill'
];

export default function AddressCapitalForm({
  selectedCompanyType,
  registrationId,
  onSubmitSuccess,
  onPrevious,
  onShowToast
}: AddressCapitalFormProps) {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [persons, setPersons] = useState<PersonData[]>([]);
  const [companyAddress, setCompanyAddress] = useState<string>('');
  const [dataLoaded, setDataLoaded] = useState(false);

  // Zustand store integration
  const {
    shareCapital,
    shareRatios,
    companyAddress: zustandCompanyAddress,
    setShareCapital,
    setShareRatios,
    setCompanyAddress: setZustandCompanyAddress,
    updateLastSaved
  } = useRegistrationStore();

  const { control, handleSubmit, formState: { errors }, watch, setValue } = useForm<CompanyAddressCapitalFormData>({
    defaultValues: {
      isDirectorAddressSame: true,
      totalShareCapital: shareCapital || 0,
      shareRatios: shareRatios ? Object.entries(shareRatios).map(([personIndex, sharePercentage]) => ({
        personIndex: parseInt(personIndex),
        personName: '',
        personRole: '',
        sharePercentage
      })) : []
    }
  });

  const { fields: shareRatioFields, replace: replaceShareRatios } = useFieldArray({
    control,
    name: 'shareRatios'
  });

  const watchIsDirectorAddressSame = watch('isDirectorAddressSame');
  const watchShareRatios = watch('shareRatios');
  const watchTotalShareCapital = watch('totalShareCapital');

  // Sync form changes to Zustand store
  useEffect(() => {
    if (watchTotalShareCapital && watchTotalShareCapital > 0) {
      setShareCapital(watchTotalShareCapital);
      updateLastSaved();
    }
  }, [watchTotalShareCapital, setShareCapital, updateLastSaved]);

  useEffect(() => {
    if (watchShareRatios && Array.isArray(watchShareRatios) && watchShareRatios.length > 0) {
      const ratiosObject = watchShareRatios.reduce((acc, ratio) => {
        if (ratio && typeof ratio.personIndex === 'number' && typeof ratio.sharePercentage === 'number') {
          acc[ratio.personIndex] = ratio.sharePercentage;
        }
        return acc;
      }, {} as { [personIndex: number]: number });

      setShareRatios(ratiosObject);
      updateLastSaved();
    }
  }, [watchShareRatios, setShareRatios, updateLastSaved]);

  // Force OPC share ratios after data is loaded
  useEffect(() => {
    if (dataLoaded && selectedCompanyType && (selectedCompanyType.code === 'OPC' || selectedCompanyType.name === 'One Person Company')) {
      console.log('Force updating OPC share ratios after data loaded');

      // Get current share ratio fields
      const currentFields = shareRatioFields;
      console.log('Force update - current shareRatioFields:', currentFields);
      console.log('Force update - watchShareRatios:', watchShareRatios);

      if (currentFields && currentFields.length > 0) {
        currentFields.forEach((field, index) => {
          const correctPercentage = field.personRole === 'Owner' ? 100 : 0;
          console.log(`Force setting shareRatios.${index}.sharePercentage to ${correctPercentage} for ${field.personRole} (${field.personName})`);
          setValue(`shareRatios.${index}.sharePercentage`, correctPercentage, { shouldValidate: true, shouldDirty: true });
        });
      }
    }
  }, [dataLoaded, selectedCompanyType, shareRatioFields, setValue]);

  // Load data from Zustand store on mount
  useEffect(() => {
    // Load share capital from Zustand if available
    if (shareCapital > 0) {
      setValue('totalShareCapital', shareCapital);
    }

    // Load company address from Zustand if available
    if (zustandCompanyAddress.street) {
      const addressString = `${zustandCompanyAddress.street}, ${zustandCompanyAddress.city}, ${zustandCompanyAddress.state} ${zustandCompanyAddress.pincode}`;
      setCompanyAddress(addressString);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);

        // Get company address from Zustand store or localStorage
        const addressToUse = zustandCompanyAddress.street
          ? `${zustandCompanyAddress.street}, ${zustandCompanyAddress.city}, ${zustandCompanyAddress.state} ${zustandCompanyAddress.pincode}`
          : localStorage.getItem('companyAddress') || '';
        setCompanyAddress(addressToUse);

        // Set share capital from Zustand store or minimum based on company type
        const capitalToUse = shareCapital > 0 ? shareCapital : selectedCompanyType?.minShareCapital || 0;
        if (capitalToUse > 0) {
          setValue('totalShareCapital', capitalToUse);
        }

        // Fetch persons data from API
        const response = await axios.get(`/api/get-person-details?registrationId=${registrationId}`);
        if (response.data.success) {
          const personsData = response.data.data;
          setPersons(personsData);

          // Also fetch invite status to get total person count
          const inviteResponse = await axios.get(`/api/get-person-status?registrationId=${registrationId}`);
          let inviteCount = 0;
          if (inviteResponse.data.success) {
            const personStatuses = inviteResponse.data.data.personStatuses;
            // Count invites (exclude Person A at index 0)
            inviteCount = Object.keys(personStatuses).filter(index => {
              const personIndex = parseInt(index);
              const status = personStatuses[personIndex];
              return personIndex !== 0 && status.hasInvitation;
            }).length;
          }

          console.log('Persons from API:', personsData.length);
          console.log('Invites sent:', inviteCount);
          console.log('Total persons for share ratio:', 1 + inviteCount);

          // Initialize share ratios with total person count
          initializeShareRatios(personsData, selectedCompanyType, inviteCount);
        }

        // Mark data as loaded
        setDataLoaded(true);

      } catch (error) {
        console.error('Error loading initial data:', error);
        alert('Error loading registration data. Please try again.');
        setDataLoaded(true); // Set as loaded even on error to prevent infinite loop
      } finally {
        setLoading(false);
      }
    };

    if (selectedCompanyType && registrationId && !dataLoaded) {
      loadInitialData();
    }
  }, [selectedCompanyType, registrationId, dataLoaded]);

  // Helper function to get person roles based on company type
  const getPersonRoles = (companyType: string) => {
    switch (companyType) {
      case 'Private Limited Company':
        return ['Director 1', 'Director 2'];
      case 'One Person Company':
        return ['Owner', 'Nominee'];
      case 'Limited Liability Partnership':
        return ['Partner 1', 'Partner 2'];
      case 'Section-8 Company':
        return ['Director 1', 'Director 2'];
      default:
        return ['Person 1', 'Person 2'];
    }
  };

  const initializeShareRatios = (personsData: PersonData[], companyType: CompanyType | null, inviteCount: number = 0) => {
    if (!companyType) return;

    // Check if we have existing share ratios in Zustand store
    const hasExistingRatios = shareRatios && Object.keys(shareRatios).length > 0;

    // Calculate total persons: Person A (1) + Number of invites
    const totalPersons = 1 + inviteCount;
    const roles = getPersonRoles(companyType.name);

    console.log('Share ratio initialization:');
    console.log('- Company type:', companyType.name);
    console.log('- Persons from API:', personsData.length);
    console.log('- Invites sent:', inviteCount);
    console.log('- Total persons needed:', totalPersons);
    console.log('- Available roles:', roles);

    // Create share ratio data based on total persons (Person A + invites)
    const shareRatiosArray: Array<{
      personIndex: number;
      personName: string;
      personRole: string;
      sharePercentage: number;
    }> = [];

    // Always include Person A (index 0)
    if (personsData.length > 0) {
      shareRatiosArray.push({
        personIndex: 0,
        personName: `${personsData[0].firstName} ${personsData[0].lastName || ''}`.trim(),
        personRole: personsData[0].personRole,
        sharePercentage: companyType.code === 'OPC' ? 100 : 0
      });
    }

    // Add entries for invited persons (they will be filled later via invite)
    for (let i = 1; i < totalPersons; i++) {
      const role = roles[i] || `Person ${i + 1}`;
      shareRatiosArray.push({
        personIndex: i,
        personName: `Invited ${role}`,
        personRole: role,
        sharePercentage: companyType.code === 'OPC' ? 0 : 0
      });
    }

    if (companyType.code === 'OPC' || companyType.name === 'One Person Company') {
      // For OPC: Owner gets 100%, others get 0% - ALWAYS fixed
      // Update share percentages for OPC
      shareRatiosArray.forEach(ratio => {
        ratio.sharePercentage = ratio.personRole === 'Owner' ? 100 : 0;
      });

      console.log('OPC Share Ratios Array:', shareRatiosArray);
      console.log('Current shareRatioFields before replace:', shareRatioFields);
      replaceShareRatios(shareRatiosArray);
      console.log('shareRatioFields after replace:', shareRatioFields);

      // Also set the form values immediately and force update
      shareRatiosArray.forEach((ratio, index) => {
        setValue(`shareRatios.${index}.sharePercentage`, ratio.sharePercentage, { shouldValidate: true, shouldDirty: true });
        console.log(`Setting shareRatios.${index}.sharePercentage to ${ratio.sharePercentage} for ${ratio.personRole}`);
      });

      // Force trigger form re-render with multiple attempts
      setTimeout(() => {
        shareRatiosArray.forEach((ratio, index) => {
          setValue(`shareRatios.${index}.sharePercentage`, ratio.sharePercentage, { shouldValidate: true, shouldDirty: true });
          console.log(`Force re-setting shareRatios.${index}.sharePercentage to ${ratio.sharePercentage}`);
        });
      }, 100);

      setTimeout(() => {
        shareRatiosArray.forEach((ratio, index) => {
          setValue(`shareRatios.${index}.sharePercentage`, ratio.sharePercentage, { shouldValidate: true, shouldDirty: true });
          console.log(`Second force re-setting shareRatios.${index}.sharePercentage to ${ratio.sharePercentage}`);
        });
      }, 500);

      // Update Zustand store immediately for OPC
      const ratiosObject = shareRatiosArray.reduce((acc, ratio) => {
        acc[ratio.personIndex] = ratio.sharePercentage;
        return acc;
      }, {} as { [personIndex: number]: number });
      setShareRatios(ratiosObject);
      console.log('OPC Ratios Object for Zustand:', ratiosObject);
    } else {
      // For other company types: Use existing ratios or equal distribution
      if (hasExistingRatios) {
        // Use existing ratios from Zustand store
        shareRatiosArray.forEach(ratio => {
          ratio.sharePercentage = shareRatios[ratio.personIndex] || 0;
        });
      } else {
        // Equal distribution initially
        const equalShare = Math.floor(100 / totalPersons);
        const remainder = 100 % totalPersons;

        shareRatiosArray.forEach((ratio, index) => {
          ratio.sharePercentage = equalShare + (index < remainder ? 1 : 0);
        });
      }

      console.log('Other Company Type Share Ratios Array:', shareRatiosArray);
      replaceShareRatios(shareRatiosArray);

      // Set form values
      shareRatiosArray.forEach((ratio, index) => {
        setValue(`shareRatios.${index}.sharePercentage`, ratio.sharePercentage);
      });
    }
  };

  const getShareCapitalMessage = () => {
    if (!selectedCompanyType || !selectedCompanyType.minShareCapital) return '';

    switch (selectedCompanyType.code) {
      case 'OPC':
      case 'PVT':
        return `For ${selectedCompanyType.name}, minimum share capital starts from ₹${selectedCompanyType.minShareCapital.toLocaleString()}`;
      case 'LLP':
      case 'SECTION8':
        return `For ${selectedCompanyType.name}, minimum share capital starts from ₹${selectedCompanyType.minShareCapital.toLocaleString()}`;
      default:
        return `Minimum share capital: ₹${selectedCompanyType.minShareCapital.toLocaleString()}`;
    }
  };

  const validateShareRatios = () => {
    // For OPC, validation is always true (fixed 100% to owner)
    if (selectedCompanyType?.code === 'OPC' || selectedCompanyType?.name === 'One Person Company') {
      return true;
    }

    if (!Array.isArray(watchShareRatios)) {
      return false;
    }

    const total = watchShareRatios.reduce((sum, ratio) => {
      const percentage = Number(ratio?.sharePercentage) || 0;
      return sum + percentage;
    }, 0);

    return Math.abs(total - 100) < 0.1; // Allow tolerance for floating point differences
  };

  const onSubmit = async (data: CompanyAddressCapitalFormData) => {
    // Validate share ratios
    if (!validateShareRatios()) {
      onShowToast?.('❌ Share ratios must total exactly 100%', 'error');
      return;
    }

    try {
      setSaving(true);

      // Save data to Zustand store before API call
      setShareCapital(Number(data.totalShareCapital));

      // Convert share ratios array to object for Zustand
      const ratiosObject = data.shareRatios.reduce((acc, ratio) => {
        acc[ratio.personIndex] = Number(ratio.sharePercentage);
        return acc;
      }, {} as { [personIndex: number]: number });
      setShareRatios(ratiosObject);

      updateLastSaved();

      console.log('Submitting company address & capital data:', data);

      // Handle address proof file upload if provided
      let addressProofUrl = null;
      if (data.addressProofFile && data.addressProofFile.length > 0) {
        console.log('Uploading address proof file...');

        const formData = new FormData();
        formData.append('file', data.addressProofFile[0]);
        formData.append('documentType', 'addressproof');

        const uploadResponse = await axios.post('/api/upload-document', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (uploadResponse.data.success) {
          addressProofUrl = uploadResponse.data.fileUrl;
          console.log('Address proof uploaded successfully:', addressProofUrl);
        } else {
          throw new Error('Failed to upload address proof file');
        }
      }

      // Ensure numeric values are sent to API
      const submitData = {
        ...data,
        totalShareCapital: Number(data.totalShareCapital),
        addressProofUrl: addressProofUrl, // Add the uploaded file URL
        shareRatios: data.shareRatios.map(ratio => {
          let sharePercentage;

          // For OPC, hardcode the percentages in API submission
          if (selectedCompanyType?.code === 'OPC' || selectedCompanyType?.name === 'One Person Company') {
            sharePercentage = ratio.personRole === 'Director 1' ? 100 : 0;
          } else {
            sharePercentage = Number(ratio.sharePercentage);
          }

          return {
            ...ratio,
            sharePercentage: sharePercentage
          };
        })
      };

      console.log('Submit data being sent to API:', submitData);
      console.log('Share ratios total:', submitData.shareRatios.reduce((sum, ratio) => sum + ratio.sharePercentage, 0));

      const response = await axios.post('/api/save-company-address-capital', {
        registrationId,
        ...submitData
      });

      if (response.data.success) {
        onShowToast?.(' Company address & share capital saved successfully! Proceeding to final step...', 'success');
        onSubmitSuccess();
      } else {
        throw new Error(response.data.error || 'Failed to save data');
      }
    } catch (error) {
      console.error('Error saving company address & capital:', error);

      let errorMessage = '❌ Failed to save data. Please try again.';
      if (axios.isAxiosError(error) && error.response?.data?.error) {
        errorMessage = `❌ ${error.response.data.error}`;
      }

      alert(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Address & Capital</h1>
          <p className="text-gray-600">Step 3 of 4 - Configure address proof and share allocation</p>
          <div className="mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading registration data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="mb-8">
        <div className="text-center mb-4">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Company Address & Share Capital</h1>
          <p className="text-gray-600">Step 3 of 4 - Configure address proof and share allocation</p>
        </div>
        
        {selectedCompanyType && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800">
              <strong>Company Type:</strong> {selectedCompanyType.name}
            </p>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Section 1: Director Address Proof */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">📍 Company Address Proof</h2>
          
          <div className="space-y-4">
            <div>
              <p className="text-gray-700 mb-4">Is Company address proof is same as Director address proof?</p>
              
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="true"
                    checked={watchIsDirectorAddressSame === true}
                    onChange={() => setValue('isDirectorAddressSame', true)}
                    className="mr-2"
                  />
                  <span className="text-green-600">✅ Yes</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="false"
                    checked={watchIsDirectorAddressSame === false}
                    onChange={() => setValue('isDirectorAddressSame', false)}
                    className="mr-2"
                  />
                  <span className="text-red-600">❌ No</span>
                </label>
              </div>
            </div>

            {watchIsDirectorAddressSame === true && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">Company Address (Auto-filled)</h4>
                <p className="text-green-800 text-sm">
                  {companyAddress || 'Company address will be used from Step 1'}
                </p>
              </div>
            )}

            {watchIsDirectorAddressSame === false && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Address Proof Type *
                  </label>
                  <select
                    {...control.register('addressProofType', {
                      required: watchIsDirectorAddressSame === false ? 'Address proof type is required' : false
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select proof type...</option>
                    {addressProofOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  {errors.addressProofType && (
                    <p className="text-red-500 text-sm mt-1">{errors.addressProofType.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Upload Address Proof *
                  </label>
                  <input
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    {...control.register('addressProofFile', {
                      required: watchIsDirectorAddressSame === false ? 'Address proof file is required' : false
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  {errors.addressProofFile && (
                    <p className="text-red-500 text-sm mt-1">{errors.addressProofFile.message}</p>
                  )}
                  <p className="text-gray-500 text-xs mt-1">
                    Supported formats: PDF, JPG, PNG (Max 5MB)
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Section 2: Share Capital Input */}
        <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">💰 Share Capital</h2>

          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm">
                {getShareCapitalMessage()}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Total Share Capital Amount *
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
                <input
                  type="number"
                  min={selectedCompanyType?.minShareCapital || 100000}
                  {...control.register('totalShareCapital', {
                    required: 'Share capital is required',
                    valueAsNumber: true,
                    min: {
                      value: selectedCompanyType?.minShareCapital || 100000,
                      message: `Minimum share capital is ₹${(selectedCompanyType?.minShareCapital || 100000).toLocaleString()}`
                    }
                  })}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter amount"
                />
              </div>
              {errors.totalShareCapital && (
                <p className="text-red-500 text-sm mt-1">{errors.totalShareCapital.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Section 3: Share Ratio Allocation */}
        {selectedCompanyType?.code !== 'OPC' && selectedCompanyType?.name !== 'One Person Company' && (
          <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">📊 Share Ratio Allocation</h2>

            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 text-sm">
                  💡 Allocate share percentages among all persons. Total must equal 100%.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {shareRatioFields.map((field, index) => (
                  <div key={field.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {field.personName}
                        </h4>
                        <p className="text-sm text-gray-600">{field.personRole}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          {...control.register(`shareRatios.${index}.sharePercentage`, {
                            required: 'Share percentage is required',
                            valueAsNumber: true,
                            min: { value: 0, message: 'Percentage cannot be negative' },
                            max: { value: 100, message: 'Percentage cannot exceed 100%' }
                          })}
                          className="w-20 px-2 py-1 border border-gray-300 rounded text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <span className="text-gray-600">%</span>
                      </div>
                    </div>

                    {/* Hidden fields for person data */}
                    <input type="hidden" {...control.register(`shareRatios.${index}.personIndex`)} />
                    <input type="hidden" {...control.register(`shareRatios.${index}.personName`)} />
                    <input type="hidden" {...control.register(`shareRatios.${index}.personRole`)} />
                  </div>
                ))}
              </div>

              {/* Total Validation */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-blue-900">Total Allocation:</span>
                  <span className={`font-bold text-lg ${
                    validateShareRatios() ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {watchShareRatios ?
                      watchShareRatios.reduce((sum, ratio) => sum + (Number(ratio?.sharePercentage) || 0), 0).toFixed(2)
                      : '0.00'
                    }%
                  </span>
                </div>
                {!validateShareRatios() && (
                  <p className="text-red-600 text-sm mt-2">
                    ⚠️ Total must equal 100%. Please adjust the percentages.
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* OPC Share Information */}
        {(selectedCompanyType?.code === 'OPC' || selectedCompanyType?.name === 'One Person Company') && (
          <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">📊 Share Allocation (OPC)</h2>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm mb-4">
                💡 For One Person Company (OPC), share allocation is automatically set:
              </p>

              <div className="space-y-2">
                {shareRatioFields.map((field) => (
                  <div key={field.id} className="flex justify-between items-center">
                    <div>
                      <span className="font-medium text-blue-900">{field.personName}</span>
                      <span className="text-blue-700 text-sm ml-2">({field.personRole})</span>
                    </div>
                    <span className="font-bold text-blue-900">
                      {field.personRole === 'Director 1' ? '100%' : '0%'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onPrevious}
            className="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Previous
          </button>

          <button
            type="submit"
            disabled={saving}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Saving...</span>
              </div>
            ) : (
              'Save & Continue to Final Step'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
