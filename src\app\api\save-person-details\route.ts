import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      firstName,
      middleName,
      lastName,
      email,
      phone,
      state,
      companyName,
      alternateName,
      businessKeywords,
      generatedObjective,
      companyType
    } = await request.json();

    // Validation
    if (!firstName?.trim()) {
      return NextResponse.json(
        { error: 'First name is required' },
        { status: 400 }
      );
    }

    if (!state?.trim()) {
      return NextResponse.json(
        { error: 'State is required' },
        { status: 400 }
      );
    }

    if (!companyName?.trim()) {
      return NextResponse.json(
        { error: 'Company name is required' },
        { status: 400 }
      );
    }

    if (!businessKeywords?.trim()) {
      return NextResponse.json(
        { error: 'Business keywords are required' },
        { status: 400 }
      );
    }

    // Check if user already has basic company details (update vs create)
    const existingBasicCompanyDetail = await prisma.basicCompanyDetailStep1.findFirst({
      where: { userId: user.id }
    });

    let basicCompanyDetail;

    if (existingBasicCompanyDetail) {
      // Update existing record
      basicCompanyDetail = await prisma.basicCompanyDetailStep1.update({
        where: { id: existingBasicCompanyDetail.id },
        data: {
          firstName,
          middleName: middleName || null,
          lastName: lastName || null,
          email: email || null,
          phone: phone || null,
          state,
          companyName,
          alternateName: alternateName || null,
          businessKeywords,
          generatedObjective: generatedObjective || null,
          companyType: companyType || null,
        }
      });
    } else {
      // Create new record
      basicCompanyDetail = await prisma.basicCompanyDetailStep1.create({
        data: {
          userId: user.id,
          firstName,
          middleName: middleName || null,
          lastName: lastName || null,
          email: email || null,
          phone: phone || null,
          state,
          companyName,
          alternateName: alternateName || null,
          businessKeywords,
          generatedObjective: generatedObjective || null,
          companyType: companyType || null,
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: existingBasicCompanyDetail ? 'Basic company details updated successfully' : 'Basic company details saved successfully',
      data: basicCompanyDetail
    });

  } catch (error) {
    console.error('Error saving basic company details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
