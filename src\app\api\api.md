# 📋 API DOCUMENTATION

---

## 🔐 Authentication APIs (`/api/auth/`)

| Endpoint                        | Purpose                                 | When Called                  |
| ------------------------------- | --------------------------------------- | ---------------------------- |
| `/api/auth/signup/route.ts`     | User registration with OTP verification | Signup form submission       |
| `/api/auth/verify-otp/route.ts` | Verify OTP sent to user's phone         | After user enters OTP code   |
| `/api/auth/login/route.ts`      | User login with credentials             | Login form submission        |
| `/api/auth/logout/route.ts`     | User logout and session cleanup         | Logout button click          |
| `/api/auth/me/route.ts`         | Get current logged-in user details      | Page loads, dashboard access |
| `/api/auth/admin/route.ts`      | Admin-specific authentication           | Admin dashboard access       |

---

## 🏢 Company Registration APIs

| Endpoint                                     | Purpose                                  | When Called                        |
| -------------------------------------------- | ---------------------------------------- | ---------------------------------- |
| `/api/check-company-name/route.ts`           | Check if company name is available       | User types company name in Step 1  |
| `/api/generate-objective/route.ts`           | AI-generated company objectives          | "Generate Objectives" button click |
| `/api/get-company-details/route.ts`          | Retrieve saved company information       | Page refresh, form continuation    |
| `/api/save-company-address-capital/route.ts` | Save company address and capital details | Step 3 form submission             |
| `/api/submit-registration/route.ts`          | Final registration submission            | Review & Submit button click       |
| `/api/get-registration-status/route.ts`      | Get current registration status          | Page load, navigation decisions    |

---

## 👥 Person Management APIs

| Endpoint                                  | Purpose                           | When Called                          |
| ----------------------------------------- | --------------------------------- | ------------------------------------ |
| `/api/save-person-details/route.ts`       | Save person details for Step 2    | Person form submission in Step 2     |
| `/api/save-person-details-step2/route.ts` | Alternative person details saving | Specific Step 2 person data handling |
| `/api/get-person-details/route.ts`        | Retrieve saved person information | Form pre-filling, data restoration   |
| `/api/get-person-status/route.ts`         | Get person completion status      | Step navigation, progress tracking   |

---

## 📄 Document Management APIs

| Endpoint                              | Purpose                            | When Called                         |
| ------------------------------------- | ---------------------------------- | ----------------------------------- |
| `/api/save-person-documents/route.ts` | Save uploaded document information | Document upload completion (Step 5) |
| `/api/upload-document/route.ts`       | Upload files to S3 storage         | Document file selection and upload  |

---

## ✅ Verification APIs

| Endpoint                                | Purpose                              | When Called                                |
| --------------------------------------- | ------------------------------------ | ------------------------------------------ |
| `/api/verify-aadhaar/route.ts`          | Aadhaar number verification          | Aadhaar verification in person forms       |
| `/api/verify-pan/route.ts`              | PAN number verification              | PAN verification in person forms           |
| `/api/verify-din/route.ts`              | DIN (Director Identification Number) | DIN verification in person forms           |
| `/api/verify-electricity-bill/route.ts` | Electricity bill verification        | Address verification through utility bills |

---

## 📧 Invitation System APIs

| Endpoint                                    | Purpose                                        | When Called                             |
| ------------------------------------------- | ---------------------------------------------- | --------------------------------------- |
| `/api/send-person-invite/route.ts`          | Send invitation links to other persons         | "Send Invite" button click in Step 2    |
| `/api/validate-invite/route.ts`             | Validate invitation tokens and get invite data | Accessing invite link `/invite/[token]` |
| `/api/save-invited-person-details/route.ts` | Save details from invited person               | Invited person submits their form       |

---

## 👤 User Dashboard APIs

| Endpoint                               | Purpose                                   | When Called                                  |
| -------------------------------------- | ----------------------------------------- | -------------------------------------------- |
| `/api/get-user-registrations/route.ts` | Get all user's registration applications  | Displaying user's applications on dashboard  |
| `/api/can-create-new-form/route.ts`    | Check if user can create new registration | Preventing multiple incomplete registrations |

---

## 🔧 Admin Panel APIs (`/api/admin/`)

| Endpoint                              | Purpose                                | When Called                   |
| ------------------------------------- | -------------------------------------- | ----------------------------- |
| `/api/admin/dashboard-stats/route.ts` | Get admin dashboard statistics         | Admin dashboard load          |
| `/api/admin/users/route.ts`           | Get all users with registration counts | Admin user management section |
| `/api/admin/applications/route.ts`    | Get all applications with filtering    | Admin applications management |
