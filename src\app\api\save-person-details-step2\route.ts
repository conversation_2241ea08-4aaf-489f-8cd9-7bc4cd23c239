import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

// Save multiple persons' details for a specific company registration Step 2.


export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { persons, companyType, registrationId } = await request.json();

    // Validation
    if (!persons || !Array.isArray(persons) || persons.length === 0) {
      return NextResponse.json(
        { error: 'At least one person is required' },
        { status: 400 }
      );
    }

    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    // Validate each person (skip validation for persons that are already completed via invite)
    for (let i = 0; i < persons.length; i++) {
      const person = persons[i];

      // Check if this person is already completed via invite OR has pending invite
      const existingPerson = await prisma.personDetail.findFirst({
        where: {
          userId: user.id,
          registrationId,
          personIndex: i,
          isCompleted: true
        }
      });

      // Check if this person has a pending invitation
      const pendingInvite = await prisma.personInvitation.findFirst({
        where: {
          userId: user.id,
          registrationId,
          personIndex: i,
          status: 'PENDING'
        }
      });

      // Skip validation if person is already completed OR has pending invite
      if (existingPerson) {
        console.log(`Person ${i + 1} already completed via invite, skipping validation`);
        continue;
      }

      if (pendingInvite) {
        console.log(`Person ${i + 1} has pending invite, skipping validation`);
        continue;
      }

      // Validate only non-completed persons
      if (!person.firstName?.trim()) {
        return NextResponse.json(
          { error: `First name is required for person ${i + 1}` },
          { status: 400 }
        );
      }
      if (!person.email?.trim()) {
        return NextResponse.json(
          { error: `Email is required for person ${i + 1}` },
          { status: 400 }
        );
      }
      if (!person.contact?.trim()) {
        return NextResponse.json(
          { error: `Contact is required for person ${i + 1}` },
          { status: 400 }
        );
      }
    }

    // Save each person's details
    const savedPersons = [];

    for (let i = 0; i < persons.length; i++) {
      const person = persons[i];

      // Check if this person is already completed via invite
      const existingPerson = await prisma.personDetail.findFirst({
        where: {
          userId: user.id,
          registrationId,
          personIndex: i,
          isCompleted: true
        }
      });

      // Check if this person has a pending invitation
      const pendingInvite = await prisma.personInvitation.findFirst({
        where: {
          userId: user.id,
          registrationId,
          personIndex: i,
          status: 'PENDING'
        }
      });

      // Skip saving if person is already completed
      if (existingPerson) {
        console.log(`Person ${i + 1} already completed via invite, skipping save`);
        savedPersons.push(existingPerson);
        continue;
      }

      // Skip saving if person has pending invite (they will fill via invite link)
      if (pendingInvite) {
        console.log(`Person ${i + 1} has pending invite, skipping save`);
        // Don't add to savedPersons as they haven't completed yet
        continue;
      }

      // Determine person role based on company type and index
      let personRole = '';
      if (companyType === 'OPC') {
        personRole = i === 0 ? 'Owner' : 'Nominee';
      } else if (companyType === 'LLP') {
        personRole = `Partner ${i + 1}`;
      } else if (companyType === 'Section-8') {
        personRole = `Director ${i + 1}`;
      } else { // Pvt Ltd
        personRole = `Director ${i + 1}`;
      }

      const personDetail = await prisma.personDetail.create({
        data: {
          userId: user.id,
          registrationId,
          personIndex: i,
          personRole,
          
          // Basic Information
          firstName: person.firstName,
          middleName: person.middleName || null,
          lastName: person.lastName || null,
          email: person.email,
          contact: person.contact,
          
          // Aadhaar Verification
          aadhaarVerified: person.aadhaarVerified || false,
          aadhaarData: person.aadhaarData || null,

          // PAN Verification
          panVerified: person.panVerified || false,
          panData: person.panData || null,

          // DIN Verification
          hasDIN: person.hasDIN || false,
          dinVerified: person.dinVerified || false,
          dinData: person.dinData || null,



          // Electricity Verification
          electricityConnectionNumber: person.electricityConnectionNumber || null,
          electricityBoardName: person.electricityBoardName || null,
          electricityVerified: person.electricityVerified || false,
          electricityData: person.electricityData || null,
          
          // Status
          isCompleted: true,
          documentsCompleted: false, // Person A needs to upload documents separately
          completedAt: new Date()
        }
      });
      
      savedPersons.push(personDetail);
    }

    return NextResponse.json({
      success: true,
      message: `Successfully saved details for ${persons.length} person(s)`,
      data: {
        registrationId,
        companyType,
        persons: savedPersons
      }
    });

  } catch (error) {
    console.error('Error saving person details step 2:', error);

    // More detailed error logging
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
