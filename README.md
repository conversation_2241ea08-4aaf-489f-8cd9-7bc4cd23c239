# TaxLegit MVP

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- <PERSON><PERSON> and <PERSON>er Compose

### Setup Instructions

1. Clone the repository:
```bash
git clone [repository-url]
cd taxlegitmvp
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with the following content:
```
DATABASE_URL="postgresql://postgres:123456@localhost:5432/mvp?schema=public"
```

4. Start the database using Docker:
```bash
docker-compose up -d
```

5. Set up the database and seed data:
```bash
# Generate Prisma client
npm run prisma:generate

# Apply migrations
npm run prisma:migrate

# Seed the database with initial data
npm run prisma:seed
```

6. Start the development server:
```bash
npm run dev
```

The application will be available at http://localhost:3000

### Database Management

- To reset the database (this will clear all data and re-run seeds):
```bash
npm run db:reset
```

- To add new seed data, modify the `prisma/seed.ts` file

### Default Users

After seeding, the following test users will be available:

1. Admin User
   - Email: <EMAIL>
   - Password: admin123

2. Test User
   - Email: <EMAIL>
   - Password: test123

### Database Schema

The current schema includes:

- User Model:
  - id (UUID)
  - name (String)
  - email (String, unique)
  - contact (String, unique)
  - password (String, hashed)
  - createdAt (DateTime)

## Prerequisites
- Docker and Docker Compose
- Node.js 18+ and npm

## Setup Instructions

1. Clone the repository
```bash
git clone <repository-url>
cd taxlegitmvp
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.example .env
```

4. Start Docker containers
```bash
docker-compose up -d
```

5. Run Prisma migrations
```bash
npx prisma generate
npx prisma db push
```

6. Start the development server
```bash
npm run dev
```

## Database Access

### Using pgAdmin 4
1. Access pgAdmin at http://localhost:5050
2. Login credentials:
   - Email: <EMAIL>
   - Password: admin
3. Add new server in pgAdmin:
   - Host: postgres (or localhost)
   - Port: 5432
   - Database: mvp
   - Username: postgres
   - Password: 123456

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server

## Database Management

- Generate Prisma client: `npx prisma generate`
- Push schema changes: `npx prisma db push`
- Reset database: `npx prisma db reset`

## Project Structure

```
taxlegitmvp/
├── src/
│   ├── app/           # Next.js app directory
│   ├── lib/          # Shared libraries
│   └── components/   # React components
├── prisma/
│   └── schema.prisma # Database schema
├── docker-compose.yml # Docker configuration
└── package.json
```

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
