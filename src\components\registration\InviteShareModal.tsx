'use client';

import { useState } from 'react';

interface InviteShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  inviteLink: string;
  personRole: string;
  inviteeName: string;
  inviterName: string;
}

export default function InviteShareModal({
  isOpen,
  onClose,
  inviteLink,
  personRole,
  inviteeName,
  inviterName
}: InviteShareModalProps) {
  const [copied, setCopied] = useState(false);

  if (!isOpen) return null;

  const whatsappMessage = `Hi ${inviteeName}! 

${inviterName} has invited you to complete your details as ${personRole} for their company registration with TaxLegit.

Please click the link below to fill your information:
${inviteLink}

This link will expire in 7 days. Thank you!`;

  const emailSubject = `Complete Your Details - ${personRole} | TaxLegit`;
  const emailBody = `Dear ${inviteeName},

${inviterName} has invited you to complete your details as ${personRole} for their company registration with TaxLegit.

Please click the link below to fill your information:
${inviteLink}

This invitation will expire in 7 days.

If you have any questions, please contact ${inviterName} directly.

Best regards,
TaxLegit Team`;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const shareViaWhatsApp = () => {
    const encodedMessage = encodeURIComponent(whatsappMessage);
    window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
  };

  const shareViaEmail = () => {
    const encodedSubject = encodeURIComponent(emailSubject);
    const encodedBody = encodeURIComponent(emailBody);
    window.open(`mailto:?subject=${encodedSubject}&body=${encodedBody}`, '_blank');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900">Share Invitation</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* Invite Link */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Invitation Link
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={inviteLink}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm"
              />
              <button
                onClick={() => copyToClipboard(inviteLink)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm"
              >
                {copied ? 'Copied!' : 'Copy'}
              </button>
            </div>
          </div>

          {/* Quick Share Buttons */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Quick Share
            </label>
            <div className="flex space-x-3">
              <button
                onClick={shareViaWhatsApp}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <span className="mr-2">📱</span>
                WhatsApp
              </button>
              <button
                onClick={shareViaEmail}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <span className="mr-2">📧</span>
                Email
              </button>
            </div>
          </div>

          {/* WhatsApp Message Preview */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              WhatsApp Message Preview
            </label>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap font-sans">
                {whatsappMessage}
              </pre>
            </div>
            <button
              onClick={() => copyToClipboard(whatsappMessage)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800"
            >
              Copy WhatsApp message
            </button>
          </div>

          {/* Email Preview */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Preview
            </label>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="text-sm text-gray-700 mb-2">
                <strong>Subject:</strong> {emailSubject}
              </div>
              <div className="text-sm text-gray-700">
                <strong>Body:</strong>
              </div>
              <pre className="text-sm text-gray-700 whitespace-pre-wrap font-sans mt-1">
                {emailBody}
              </pre>
            </div>
            <button
              onClick={() => copyToClipboard(emailBody)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800"
            >
              Copy email content
            </button>
          </div>
        </div>

        <div className="flex justify-end mt-6 pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
