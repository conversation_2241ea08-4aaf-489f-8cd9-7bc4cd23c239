"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";

export default function DigilockerCallback() {
  const [status, setStatus] = useState<"loading" | "success" | "error">("loading");
  const [message, setMessage] = useState("");
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleCallback = () => {
      try {
        // Get parameters from URL
        const code = searchParams.get("code");
        const state = searchParams.get("state");
        const error = searchParams.get("error");
        const clientId = searchParams.get("client_id");

        if (error) {
          setStatus("error");
          setMessage(`DigiLocker authentication failed: ${error}`);
          // Notify parent window about the error
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "DIGILOCKER_ERROR",
                error: error,
              },
              window.location.origin
            );
          }
          return;
        }

        if (code && clientId) {
          setStatus("success");
          setMessage("DigiLocker authentication successful! Processing your Aadhaar data...");
          
          // Notify parent window about success
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "DIGILOCKER_SUCCESS",
                code: code,
                clientId: clientId,
                state: state,
              },
              window.location.origin
            );
          }

          // Close the popup after a short delay
          setTimeout(() => {
            window.close();
          }, 2000);
        } else {
          setStatus("error");
          setMessage("Missing required parameters from DigiLocker callback");
          
          if (window.opener) {
            window.opener.postMessage(
              {
                type: "DIGILOCKER_ERROR",
                error: "Missing required parameters",
              },
              window.location.origin
            );
          }
        }
      } catch (err) {
        console.error("DigiLocker callback error:", err);
        setStatus("error");
        setMessage("An error occurred while processing the DigiLocker callback");
        
        if (window.opener) {
          window.opener.postMessage(
            {
              type: "DIGILOCKER_ERROR",
              error: "Callback processing failed",
            },
            window.location.origin
          );
        }
      }
    };

    handleCallback();
  }, [searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          {status === "loading" && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Processing DigiLocker Response
              </h2>
              <p className="text-gray-600">Please wait while we process your authentication...</p>
            </>
          )}

          {status === "success" && (
            <>
              <div className="text-green-500 text-5xl mb-4">✅</div>
              <h2 className="text-xl font-semibold text-green-900 mb-2">
                Authentication Successful!
              </h2>
              <p className="text-gray-600 mb-4">{message}</p>
              <p className="text-sm text-gray-500">
                This window will close automatically...
              </p>
            </>
          )}

          {status === "error" && (
            <>
              <div className="text-red-500 text-5xl mb-4">❌</div>
              <h2 className="text-xl font-semibold text-red-900 mb-2">
                Authentication Failed
              </h2>
              <p className="text-gray-600 mb-4">{message}</p>
              <button
                onClick={() => window.close()}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Close Window
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
