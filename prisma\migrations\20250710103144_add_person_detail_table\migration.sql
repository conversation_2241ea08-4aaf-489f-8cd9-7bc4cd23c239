-- CreateTable
CREATE TABLE "PersonDetail" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "middleName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "state" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "alternateName" TEXT,
    "businessKeywords" TEXT NOT NULL,
    "generatedObjective" TEXT,
    "companyType" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PersonDetail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PersonDetail_userId_idx" ON "PersonDetail"("userId");

-- AddForeignKey
ALTER TABLE "PersonDetail" ADD CONSTRAINT "PersonDetail_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
