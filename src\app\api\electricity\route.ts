import { NextRequest, NextResponse } from "next/server";

// Cache variables - file ke top par rakhein taaki GET aur POST dono use kar sakein
let operatorListCache: any[] | null = null;
let lastFetchTime: number = 0;
const CACHE_DURATION_MS = 60 * 60 * 1000; // 1 ghante ka cache

// ===================================================================
//  HANDLE GET REQUEST: To fetch the list of electricity operators
// ===================================================================
export async function GET(request: NextRequest) {
  // Check if a fresh cache exists
  if (operatorListCache && Date.now() - lastFetchTime < CACHE_DURATION_MS) {
    console.log("Returning electricity operator list from cache.");
    return NextResponse.json({ success: true, data: operatorListCache });
  }

  try {
    console.log("Fetching new electricity operator list from SurePass...");
    const surepassApiUrl =
      "https://sandbox.surepass.io/api/v1/utility/electricity/operator-code-list";
    const surepassApiKey = process.env.SUREPASS_API_TOKEN;

    if (!surepassApiKey) {
      throw new Error("SUREPASS_API_TOKEN is not configured.");
    }

    const response = await fetch(surepassApiUrl, {
      method: "GET",
      headers: { Authorization: `Bearer ${surepassApiKey}` },
    });

    if (!response.ok)
      throw new Error(
        `Failed to fetch operator list. Status: ${response.status}`
      );

    const result = await response.json();
    if (!result.success || !result.data)
      throw new Error("SurePass API returned success=false or no data.");

    // Update cache
    operatorListCache = result.data;
    lastFetchTime = Date.now();

    return NextResponse.json({ success: true, data: operatorListCache });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred.";
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

// ===================================================================
//  HANDLE POST REQUEST: To verify the electricity bill
// ===================================================================
export async function POST(request: NextRequest) {
  try {
    const { connectionNumber, operatorCode } = await request.json();

    if (!connectionNumber || !operatorCode) {
      return NextResponse.json(
        {
          success: false,
          error: "Connection number and operator code are required",
        },
        { status: 400 }
      );
    }

    const surepassApiUrl =
      "https://sandbox.surepass.io/api/v1/utility/electricity/";
    const surepassApiKey = process.env.SUREPASS_API_TOKEN;

    if (!surepassApiKey) {
      return NextResponse.json(
        { success: false, error: "Server configuration error." },
        { status: 500 }
      );
    }

    const requestBody = {
      id_number: connectionNumber,
      operator_code: operatorCode,
    };

    const surepassResponse = await fetch(surepassApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${surepassApiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await surepassResponse.json();

    if (!surepassResponse.ok) {
      return NextResponse.json(
        {
          success: false,
          error: responseData.message || "Verification failed.",
          details: responseData,
        },
        { status: surepassResponse.status }
      );
    }

    return NextResponse.json({ success: true, data: responseData.data });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error.",
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
