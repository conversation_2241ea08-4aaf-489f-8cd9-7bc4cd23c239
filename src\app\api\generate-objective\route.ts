// pages/api/generate-objective.ts or app/api/generate-objective/route.ts
// (Adjust path based on your Next.js version and project structure)

import { NextRequest, NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai";

// Initialize Gemini API client
// IMPORTANT: Access API key securely from environment variables
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

export async function POST(request: NextRequest) {
  try {
    const { keywords, companyType } = await request.json();

    // 1. Input Validation
    if (!keywords?.trim()) {
      return NextResponse.json(
        { error: "Business keywords are required" },
        { status: 400 }
      );
    }

    // 2. API Key Check (Crucial for production)
    if (!process.env.GEMINI_API_KEY) {
      console.error("GEMINI_API_KEY environment variable not set.");
      return NextResponse.json(
        { error: "Server configuration error: Gemini API key is missing." },
        { status: 500 }
      );
    }

    // 3. Get Gemini Model
    // 'gemini-pro' is suitable for text-only generation.
    const model = genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite-001",
    });

    // 4. Prepare Keywords
    const keywordArray = keywords
      .toLowerCase()
      .split(/[,\s]+/)
      .filter((k: string) => k.length > 0);
    // Use up to 3 keywords for a concise prompt
    const formattedKeywords = keywordArray.slice(0, 3).join(", ");

    // 5. Construct a Clear and Specific Prompt for Gemini
    // A good prompt is key to getting relevant and useful output from the AI.
    const prompt = `Draft a formal, legally sound, and impactful company objective suitable for incorporation under Indian company law.

The company is a "${companyType}" and will engage in core business activities related to: ${formattedKeywords}.

The objective should:
- Be 1–2 sentences long
- Clearly describe the primary business operations and value proposition
- Reflect intent for lawful and profit-driven (or social-welfare-driven for Section-8) operations
- Align with standard MCA terminology and compliance language

Ensure the objective reflects long-term growth, ethical practices, and value creation for clients, stakeholders, and society. Avoid vague expressions; use concrete, professional language.`;

    // 6. Call Gemini API
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const generatedObjective = response.text(); // Extract the plain text from the AI's response

    // 7. Return the Generated Objective
    // Removed the artificial delay as the AI call has its own latency.
    return NextResponse.json({
      objective: generatedObjective,
      keywords: keywordArray, // You might still want to return these for frontend debugging/display
      companyType: companyType,
    });
  } catch (error) {
    console.error("Error generating objective with Gemini API:", error);
    // Return a generic error message to the client for security and user experience
    return NextResponse.json(
      {
        error:
          "Failed to generate objective. Please check your inputs and try again, or contact support.",
      },
      { status: 500 }
    );
  }
}
