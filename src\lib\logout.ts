import axios from 'axios';
import { useRegistrationStore } from '@/store/registrationStore';

/**
 * Utility function to handle complete logout process
 * Clears all user data including Zustand store and localStorage
 */
export const performLogout = async () => {
  try {
    // Call logout API to clear server-side session/cookies
    await axios.post('/api/auth/logout');
  } catch (error) {
    console.error('Logout API failed:', error);
    // Continue with client-side cleanup even if API fails
  }

  // Clear Zustand store data
  const { clearFormData } = useRegistrationStore.getState();
  clearFormData();

  // Clear localStorage items
  localStorage.removeItem('registration-storage');
  localStorage.removeItem('token'); // For admin users
  localStorage.removeItem('auth-token'); // If any other auth tokens exist
  localStorage.removeItem('companyAddress'); // Clear any other registration data

  // Clear sessionStorage as well (in case any data is stored there)
  sessionStorage.clear();
};

/**
 * Hook version for use in React components
 */
export const useLogout = () => {
  const { clearFormData } = useRegistrationStore();

  const logout = async () => {
    try {
      // Call logout API to clear server-side session/cookies
      await axios.post('/api/auth/logout');
    } catch (error) {
      console.error('Logout API failed:', error);
      // Continue with client-side cleanup even if API fails
    }

    // Clear Zustand store data
    clearFormData();

    // Clear localStorage items
    localStorage.removeItem('registration-storage');
    localStorage.removeItem('token'); // For admin users
    localStorage.removeItem('auth-token'); // If any other auth tokens exist
    localStorage.removeItem('companyAddress'); // Clear any other registration data

    // Clear sessionStorage as well (in case any data is stored there)
    sessionStorage.clear();
  };

  return logout;
};
