import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateToken } from '@/lib/auth';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find admin user in database
    const user = await prisma.user.findFirst({
      where: {
        AND: [
          { email },
          { role: 'ADMIN' },
        ],
      },
    });

    // Admin must exist
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check if admin has password set
    if (!user.password) {
      return NextResponse.json(
        { error: 'Admin account not properly configured. Please contact system administrator.' },
        { status: 500 }
      );
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Construct full name from firstName, middleName, lastName
    const fullName = [user.firstName, user.middleName, user.lastName]
      .filter(Boolean)
      .join(' ');

    // Generate JWT token
    const token = generateToken({
      id: user.id,
      firstName: user.firstName,
      middleName: user.middleName || undefined,
      lastName: user.lastName || undefined,
      email: user.email || undefined,
      contact: user.contact || undefined,
      role: user.role,
    });

    // Create response with token in cookie
    const response = NextResponse.json({
      message: 'Admin login successful',
      user: {
        id: user.id,
        name: fullName,
        email: user.email,
        contact: user.contact,
        role: user.role,
      },
      redirectUrl: '/admin/dashboard',
    });

    // Set HTTP-only cookie
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    });

    return response;

  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
