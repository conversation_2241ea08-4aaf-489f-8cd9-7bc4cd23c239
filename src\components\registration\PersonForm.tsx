"use client";

import { useState, useEffect } from "react";
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  useWatch,
} from "react-hook-form";
import AadhaarVerification from "./AadhaarVerification";
import PANVerification from "./PANVerification";
import DINVerification from "./DINVerification";

import AddressVerification from "./AddressVerification";

interface PersonFormData {
  persons: Array<{
    firstName: string;
    middleName?: string;
    lastName?: string;
    email: string;
    contact: string;
    aadhaarNumber?: string;
    aadhaarVerified: boolean;
    aadhaarData?: unknown;
    panNumber?: string;
    panVerified: boolean;
    panData?: unknown;
    hasDIN?: boolean;
    dinNumber?: string;
    dinVerified: boolean;
    dinData?: unknown;

    electricityConnectionNumber?: string;
    electricityOperatorCode?: string; // <-- Ise 'electricityBoardName' se badal dein
    electricityVerified?: boolean;
    electricityData?: unknown;
    addressVerificationMethod?: string;
  }>;
}

interface PersonFormProps {
  index: number;
  role: string;
  control: Control<PersonFormData>;
  errors: FieldErrors<PersonFormData>;
  setValue: UseFormSetValue<PersonFormData>;
  canRemove: boolean;
  onRemove: () => void;
  isFirstPerson: boolean;
  onGetLink?: (personData: { personRole: string; personIndex: number }) => void;
  onLinkCopied?: (message: string) => void;
  inviteStatus?: "none" | "pending" | "form_submitted" | "documents_submitted";
  documentsCompleted?: boolean;
  inviteLink?: string;
  inviteExpiry?: Date;
  onShowToast?: (message: string, type: "success" | "error" | "info") => void;
}

export default function PersonForm({
  index,
  role,
  control,
  errors,
  setValue,
  canRemove,
  onRemove,
  isFirstPerson,
  onGetLink,
  onLinkCopied,
  inviteStatus = "none",
  documentsCompleted = false,
  inviteLink,
  inviteExpiry,
  onShowToast,
}: PersonFormProps) {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({
    basic: true,
    aadhaar: true, // Always open
    pan: true, // Always open
    din: true, // Always open
    address: true, // Always open
  });
  const [linkCopied, setLinkCopied] = useState(false);

  // Watch the address verification method to show/hide verification forms
  const watchAddressMethod = useWatch({
    control,
    name: `persons.${index}.addressVerificationMethod`,
  });

  // Update expanded sections when address verification method changes
  useEffect(() => {
    console.log(
      `Person ${index} - Address method changed to:`,
      watchAddressMethod
    );
  }, [watchAddressMethod]);

  const toggleSection = (section: string) => {
    // Prevent closing main verification sections
    const alwaysOpenSections = ["aadhaar", "pan", "din", "address"];
    if (alwaysOpenSections.includes(section)) {
      return; // Don't allow closing these sections
    }

    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleGetLink = () => {
    if (onGetLink) {
      onGetLink({
        personIndex: index,
        personRole: role,
      });
    }
  };

  const getInviteStatusBadge = () => {
    switch (inviteStatus) {
      case "pending":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            ⏳ Invite Sent
          </span>
        );
      case "form_submitted":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            📝 Form Submitted, Documents Pending
          </span>
        );
      case "documents_submitted":
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✅ All Completed
          </span>
        );
      case "none":
        // For Person A (non-invited users), show document status
        if (documentsCompleted) {
          return (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              ✅ All Completed
            </span>
          );
        } else {
          return (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {" "}
              Form Submitted, Documents Pending
            </span>
          );
        }
      default:
        return null;
    }
  };

  // Debug logging
  console.log(
    `PersonForm ${role} (index ${index}) - inviteStatus:`,
    inviteStatus
  );

  // If person has invite (pending, form_submitted, or documents_submitted), show status instead of form
  if (
    inviteStatus === "pending" ||
    inviteStatus === "form_submitted" ||
    inviteStatus === "documents_submitted"
  ) {
    console.log(`PersonForm ${role} - Hiding form, showing status instead`);
    return (
      <div
        className={`bg-white border rounded-xl p-6 shadow-sm ${
          inviteStatus === "pending" ? "border-yellow-200" : "border-green-200"
        }`}
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-3">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center ${
                inviteStatus === "pending" ? "bg-yellow-100" : "bg-green-100"
              }`}
            >
              <span
                className={`font-semibold ${
                  inviteStatus === "pending"
                    ? "text-yellow-600"
                    : "text-green-600"
                }`}
              >
                {inviteStatus === "pending" ? "⏳" : "✓"}
              </span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{role}</h3>
              {inviteStatus === "pending" ? (
                <p className="text-sm text-yellow-600">
                  ⏳ Invite Sent - Waiting for Response
                </p>
              ) : inviteStatus === "documents_submitted" ? (
                <p className="text-sm text-green-600">
                  ✅ All Details & Documents Completed
                </p>
              ) : (
                <p className="text-sm text-blue-600">
                  📝 Form Submitted, Documents Pending
                </p>
              )}
            </div>
          </div>

          {canRemove && (
            <button
              onClick={onRemove}
              className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50"
              title="Remove person"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
            </button>
          )}
        </div>

        {/* Status Summary */}
        {inviteStatus === "pending" ? (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-900 mb-3">
              ⏳ Waiting for Invite Response
            </h4>
            <div className="space-y-3 text-sm text-yellow-800">
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">📧</span>
                </span>
                <span>Invite link has been sent to this person</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-xs">⏳</span>
                </span>
                <span>Waiting for them to fill the form via invite link</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-xs">⏳</span>
                </span>
                <span>
                  Document upload will be done by them after form submission
                </span>
              </div>
            </div>

            <div className="mt-4 pt-3 border-t border-yellow-200">
              <p className="text-xs text-yellow-700 mb-3">
                <strong>Note:</strong> This person will complete their
                registration independently via the invite link. You don't need
                to fill their details manually. Once they submit their form and
                documents, their status will be updated automatically.
              </p>

              {/* Copy Link Again Button */}
              {inviteLink && (
                <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-yellow-300">
                  <div className="flex items-center space-x-2">
                    <span className="text-yellow-600 text-sm">🔗</span>
                    <span className="text-xs text-yellow-700">
                      Need to share the invite link again?
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      navigator.clipboard.writeText(inviteLink);
                      setLinkCopied(true);
                      setTimeout(() => setLinkCopied(false), 2000);

                      // Notify parent component
                      if (onLinkCopied && inviteExpiry) {
                        onLinkCopied(
                          ` Invite link copied again! Expires: ${inviteExpiry.toLocaleDateString()}`
                        );
                      }
                    }}
                    className={`text-xs font-medium px-3 py-1 rounded transition-colors ${
                      linkCopied
                        ? "bg-green-100 text-green-800 cursor-default"
                        : "bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
                    }`}
                  >
                    {linkCopied ? "Copied!" : "Copy Link Again"}
                  </button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-medium text-green-900 mb-3">
              Registration Completed Successfully
            </h4>
            <div className="space-y-2 text-sm text-green-800">
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </span>
                <span>Personal details submitted</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </span>
                <span>Identity verification completed</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </span>
                <span>Document verification completed</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </span>
                <span>Bank details verified</span>
              </div>
            </div>

            <div className="mt-4 pt-3 border-t border-green-200">
              <p className="text-xs text-green-700 mb-3">
                This person has completed their registration via the invite
                link. All required details and verifications have been submitted
                and are ready for processing.
              </p>

              {/* Copy Link Again Option */}
              {inviteLink && (
                <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-green-300">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600 text-sm">🔗</span>
                    <span className="text-xs text-green-700">
                      Need the invite link again?
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      navigator.clipboard.writeText(inviteLink);
                      setLinkCopied(true);
                      setTimeout(() => setLinkCopied(false), 2000);

                      // Notify parent component
                      if (onLinkCopied && inviteExpiry) {
                        onLinkCopied(
                          ` Invite link copied again! Expires: ${inviteExpiry.toLocaleDateString()}`
                        );
                      }
                    }}
                    className={`text-xs font-medium underline transition-colors ${
                      linkCopied
                        ? "text-green-800 cursor-default"
                        : "text-green-600 hover:text-green-800"
                    }`}
                  >
                    {linkCopied ? "Copied!" : "Copy Link Again"}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-blue-600 font-semibold">{index + 1}</span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{role}</h3>
            {isFirstPerson && (
              <p className="text-sm text-green-600">
                Auto-filled from your account
              </p>
            )}
            {getInviteStatusBadge()}
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {!isFirstPerson && onGetLink && inviteStatus === "none" && (
            <button
              type="button"
              onClick={handleGetLink}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium flex items-center"
            >
              <span className="mr-1">🔗</span>
              Get Link
            </button>
          )}
          {canRemove && (
            <button
              type="button"
              onClick={onRemove}
              className="text-red-600 hover:text-red-800 font-medium"
            >
              Remove
            </button>
          )}
        </div>
      </div>

      {/* Basic Information */}
      <div className="mb-6">
        <button
          type="button"
          onClick={() => toggleSection("basic")}
          className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <span className="font-medium text-gray-900">
            👤 Basic Information
          </span>
          <span className="text-gray-500">
            {expandedSections.basic ? "▼" : "▶"}
          </span>
        </button>

        {expandedSections.basic && (
          <div className="mt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <input
                  {...control.register(`persons.${index}.firstName`, {
                    required: "First name is required",
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter first name"
                />
                {errors.persons?.[index]?.firstName && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.persons[index].firstName.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Middle Name
                </label>
                <input
                  {...control.register(`persons.${index}.middleName`)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter middle name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  {...control.register(`persons.${index}.lastName`)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter last name"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <input
                  type="email"
                  {...control.register(`persons.${index}.email`, {
                    required: "Email is required",
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: "Invalid email address",
                    },
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter email address"
                />
                {errors.persons?.[index]?.email && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.persons[index].email.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Number *
                </label>
                <input
                  type="tel"
                  {...control.register(`persons.${index}.contact`, {
                    required: "Contact number is required",
                    pattern: {
                      value: /^[0-9]{10}$/,
                      message: "Enter valid 10-digit contact number",
                    },
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter 10-digit contact number"
                />
                {errors.persons?.[index]?.contact && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.persons[index].contact.message}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Verification Sections */}
      <div className="space-y-6">
        {/* Row 1: Aadhaar + PAN */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <AadhaarVerification
            index={index}
            control={control}
            errors={errors}
            setValue={setValue}
            expanded={expandedSections.aadhaar}
            onToggle={() => toggleSection("aadhaar")}
            onShowToast={onShowToast}
          />

          <PANVerification
            index={index}
            control={control}
            errors={errors}
            setValue={setValue}
            // expanded={expandedSections.pan}
            // onToggle={() => toggleSection("pan")}
            onShowToast={onShowToast}
          />
        </div>

        {/* Row 2: DIN + Address Proof Selection */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <DINVerification
            index={index}
            control={control}
            errors={errors}
            setValue={setValue}
            expanded={expandedSections.din}
            onToggle={() => toggleSection("din")}
            onShowToast={onShowToast}
          />

          <AddressVerification
            index={index}
            control={control}
            errors={errors}
            setValue={setValue}
            onShowToast={onShowToast}
          />
        </div>
      </div>
    </div>
  );
}
