'use client';

import { useState } from 'react';
import { Control, FieldErrors, UseFormSetValue, useWatch } from 'react-hook-form';
import axios from 'axios';

interface PersonFormData {
  persons: Array<{
    firstName: string;
    middleName?: string;
    lastName?: string;
    email: string;
    contact: string;
    aadhaarNumber?: string;
    aadhaarVerified: boolean;
    aadhaarData?: unknown;
    panNumber?: string;
    panVerified: boolean;
    panData?: unknown;
    hasDIN?: boolean;
    dinNumber?: string;
    dinVerified: boolean;
    dinData?: unknown;

    electricityConnectionNumber?: string;
    electricityBoardName?: string;
    electricityVerified?: boolean;
    electricityData?: unknown;
    addressVerificationMethod?: string;
  }>;
}

interface DINVerificationProps {
  index: number;
  control: Control<PersonFormData>;
  errors: FieldErrors<PersonFormData>;
  setValue: UseFormSetValue<PersonFormData>;
  expanded: boolean;
  onToggle: () => void;
  onShowToast?: (message: string, type: 'success' | 'error' | 'info') => void;
}

export default function DINVerification({
  index,
  control,
  errors,
  setValue,
  expanded,
  onToggle,
  onShowToast
}: DINVerificationProps) {
  const [verifying, setVerifying] = useState(false);
  const [dinData, setDinData] = useState<{
    directorName: string;
    dateOfBirth: string;
    dinNumber: string;
    status: string;
    associatedCompanies: string;
  } | null>(null);

  // Watch form values using React Hook Form
  const hasDIN = useWatch({
    control,
    name: `persons.${index}.hasDIN`,
    defaultValue: undefined
  });
  const verified = useWatch({
    control,
    name: `persons.${index}.dinVerified`,
    defaultValue: false
  });
  const dinNumber = useWatch({
    control,
    name: `persons.${index}.dinNumber`,
    defaultValue: ''
  });

  const handleVerifyDIN = async () => {
    if (!dinNumber || dinNumber.length !== 8) {
      alert('Please enter a valid 8-digit DIN number');
      return;
    }

    try {
      setVerifying(true);
      
      // Simulate API call to verify DIN
      const response = await axios.post('/api/verify-din', {
        dinNumber,
        personIndex: index
      });

      if (response.data.success) {
        const verifiedData = response.data.data;
        setDinData(verifiedData);

        // Update form state with verified data
        setValue(`persons.${index}.dinVerified`, true);
        setValue(`persons.${index}.dinData`, verifiedData);

        onShowToast?.('✅ DIN verified successfully!', 'success');
      } else {
        throw new Error(response.data.error || 'DIN verification failed');
      }
    } catch (error) {
      console.error('DIN verification error:', error);
      onShowToast?.('Failed to verify DIN. Please check the DIN number and try again.', 'error');
    } finally {
      setVerifying(false);
    }
  };

  const getStatusBadge = () => {
    if (hasDIN === false) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">N/A</span>;
    }
    if (verified) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">✅ Verified</span>;
    }
    if (hasDIN === true) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">❌ Not Verified</span>;
    }
    return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">⏳ Pending</span>;
  };

  const formatDIN = (value: string) => {
    // Format DIN as 8-digit number
    return value.replace(/\D/g, '').slice(0, 8);
  };

  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <span className="font-medium text-gray-900">👔 DIN Verification</span>
          {getStatusBadge()}
        </div>
      </div>

      <div className="p-4 border-t border-gray-200">
          {(hasDIN === null || hasDIN === undefined) && (
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Do you have a DIN (Director Identification Number)?</h4>
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setValue(`persons.${index}.hasDIN`, true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Yes
                </button>
                <button
                  type="button"
                  onClick={() => setValue(`persons.${index}.hasDIN`, false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  No
                </button>
              </div>
            </div>
          )}

          {hasDIN === true && !verified && (
            <div className="space-y-4">
              <div className="flex space-x-3">
                <input
                  type="text"
                  {...control.register(`persons.${index}.dinNumber`, {
                    required: 'DIN number is required',
                    minLength: { value: 8, message: 'DIN must be 8 digits' },
                    maxLength: { value: 8, message: 'DIN must be 8 digits' },
                    pattern: { value: /^\d{8}$/, message: 'DIN must be 8 digits' }
                  })}
                  placeholder="Enter 8-digit DIN number"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  maxLength={8}
                />
                {errors.persons?.[index]?.dinNumber && (
                  <p className="text-red-500 text-sm mt-1">{errors.persons[index].dinNumber.message}</p>
                )}
                <button
                  type="button"
                  onClick={handleVerifyDIN}
                  disabled={verifying || !dinNumber || dinNumber.length !== 8}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center"
                >
                  {verifying ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Verifying...
                    </>
                  ) : (
                    'Verify'
                  )}
                </button>
              </div>
              <button
                type="button"
                onClick={() => {
                  setValue(`persons.${index}.hasDIN`, undefined);
                  setValue(`persons.${index}.dinNumber`, '');
                  setValue(`persons.${index}.dinVerified`, false);
                }}
                className="text-gray-600 hover:text-gray-800 text-sm"
              >
                ← Go back
              </button>
            </div>
          )}

          {hasDIN === false && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <p className="text-gray-700 text-sm">
                No DIN required. You can apply for DIN during the company registration process if needed.
              </p>
              <button
                type="button"
                onClick={() => setValue(`persons.${index}.hasDIN`, undefined)}
                className="text-blue-600 hover:text-blue-800 text-sm mt-2"
              >
                ← Change answer
              </button>
            </div>
          )}

          {verified && dinData && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-3">Verified DIN Details:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div><strong>Director Name:</strong> {dinData.directorName}</div>
                <div><strong>Date of Birth:</strong> {dinData.dateOfBirth}</div>
                <div><strong>Status:</strong> 
                  <span className={`ml-1 px-2 py-1 rounded-full text-xs ${
                    dinData.status === 'Active' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {dinData.status}
                  </span>
                </div>
                <div><strong>Associated Companies:</strong> {dinData.associatedCompanies}</div>
                <div className="md:col-span-2"><strong>DIN Number:</strong> {dinData.dinNumber}</div>
              </div>
              <button
                type="button"
                onClick={() => {
                  setValue(`persons.${index}.dinVerified`, false);
                  setDinData(null);
                  setValue(`persons.${index}.dinNumber`, '');
                }}
                className="text-blue-600 hover:text-blue-800 text-sm mt-3"
              >
                Verify different DIN
              </button>
            </div>
          )}
      </div>
    </div>
  );
}
