import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get filter from query params
    const { searchParams } = new URL(request.url);
    const filter = searchParams.get('filter') || 'all';

    // Build where clause based on filter
    let whereClause: any = {};
    
    switch (filter) {
      case 'in-progress':
        whereClause.status = 'IN_PROGRESS';
        break;
      case 'document-needed':
        whereClause.OR = [
          { status: 'SUBMITTED' },
          { status: 'DOCUMENTS_PENDING' }
        ];
        break;
      case 'completed':
        whereClause.status = 'COMPLETED';
        break;
      case 'all':
      default:
        // No filter, get all
        break;
    }

    // Get applications with user details
    const applications = await prisma.basicCompanyDetailStep1.findMany({
      where: whereClause,
      select: {
        id: true,
        companyName: true,
        companyType: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            firstName: true,
            middleName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      applications,
      filter
    });

  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
