"use client";

interface PersonDetail {
  id: string;
  invitationId?: string;
  personIndex: number;
  personRole: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email: string;
  contact: string;
  aadhaarVerified: boolean;
  panVerified: boolean;
  hasDIN: boolean;
  dinVerified: boolean;
  electricityVerified: boolean;
  sharePercentage?: number;
  isCompleted: boolean;
  documentsCompleted: boolean;
  aadhaarDocUrl?: string;
  panDocUrl?: string;
  photoUrl?: string;
  signatureUrl?: string;
  geoTagImageUrl?: string;
  bankTransactionDetailsUrl?: string;
  addressVerificationDocUrl?: string;
  createdAt: string;
  updatedAt: string;
}

interface PersonDetailCardProps {
  person: PersonDetail;
  index: number;
}
const handleView = async (fileUrl: string) => {
  try {
    // Extract S3 key from the full URL
    // URL format: https://taxlegit-documents.s3.amazonaws.com/key/path/file.ext
    // or https://taxlegit-documents.s3.ap-south-1.amazonaws.com/key/path/file.ext
    let fileKey = fileUrl;

    // If it's a full S3 URL, extract just the key part
    if (fileUrl.includes(".s3.") || fileUrl.includes(".s3-")) {
      const urlParts = fileUrl.split("/");
      const bucketIndex = urlParts.findIndex(
        (part) => part.includes(".s3.") || part.includes(".s3-")
      );
      if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {
        fileKey = urlParts.slice(bucketIndex + 1).join("/");
      }
    }

    console.log("Original URL:", fileUrl);
    console.log("Extracted key:", fileKey);

    const res = await fetch(
      `/api/view-s3-file?key=${encodeURIComponent(fileKey)}`
    );
    const data = await res.json();

    if (data.url) {
      window.open(data.url, "_blank");
    } else {
      alert("Could not generate view link");
    }
  } catch (err) {
    console.error("Error generating view URL:", err);
    alert("Something went wrong");
  }
};

export default function PersonDetailCard({
  person,
  index,
}: PersonDetailCardProps) {
  const documentUrls = [
    { name: "Aadhaar Document", url: person.aadhaarDocUrl },
    { name: "PAN Document", url: person.panDocUrl },
    { name: "Photo", url: person.photoUrl },
    { name: "Signature", url: person.signatureUrl },
    { name: "Geo Tag Image", url: person.geoTagImageUrl },
    { name: "Bank Transaction Details", url: person.bankTransactionDetailsUrl },
    {
      name: "Address Verification Document",
      url: person.addressVerificationDocUrl,
    },
  ].filter((doc) => doc.url);

  console.log(
    `Person ${person.firstName} ${person.lastName} documents:`,
    documentUrls
  );

  const handleDownloadAllDocuments = async () => {
    if (documentUrls.length === 0) {
      alert("No documents available for download");
      return;
    }

    try {
      const response = await fetch("/api/admin/download-documents", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          personId: person.id,
          documentUrls: documentUrls.map((doc) => ({
            name: doc.name,
            url: doc.url,
          })),
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.style.display = "none";
        a.href = url;
        a.download = `${person.firstName}_${person.lastName}_documents.zip`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert("Failed to download documents");
      }
    } catch (error) {
      console.error("Error downloading documents:", error);
      alert("Error downloading documents");
    }
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {person.personRole} - {person.firstName} {person.middleName}{" "}
              {person.lastName}
            </h3>
            {person.invitationId && (
              <p className="text-sm text-blue-600 mt-1">
                📧 Filled by invitation
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                person.isCompleted
                  ? "bg-green-100 text-green-800"
                  : "bg-yellow-100 text-yellow-800"
              }`}
            >
              {person.isCompleted ? "✅ Completed" : "⏳ In Progress"}
            </span>
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                person.documentsCompleted
                  ? "bg-green-100 text-green-800"
                  : "bg-orange-100 text-orange-800"
              }`}
            >
              {person.documentsCompleted
                ? "📄 Docs Complete"
                : "📄 Docs Pending"}
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold text-gray-900 border-b pb-2">
              Basic Information
            </h4>

            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <p className="text-gray-900">{person.email}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Contact
              </label>
              <p className="text-gray-900">{person.contact}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Person Index
              </label>
              <p className="text-gray-900">{person.personIndex}</p>
            </div>

            {person.sharePercentage && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Share Percentage
                </label>
                <p className="text-gray-900 font-medium">
                  {person.sharePercentage}%
                </p>
              </div>
            )}
          </div>

          {/* Verification Status */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold text-gray-900 border-b pb-2">
              Verification Status
            </h4>

            <div className="space-y-2">
              <div className="flex items-center">
                <span
                  className={`w-3 h-3 rounded-full mr-2 ${
                    person.aadhaarVerified ? "bg-green-500" : "bg-red-500"
                  }`}
                ></span>
                <span className="text-sm">
                  Aadhaar {person.aadhaarVerified ? "Verified" : "Not Verified"}
                </span>
              </div>

              <div className="flex items-center">
                <span
                  className={`w-3 h-3 rounded-full mr-2 ${
                    person.panVerified ? "bg-green-500" : "bg-red-500"
                  }`}
                ></span>
                <span className="text-sm">
                  PAN {person.panVerified ? "Verified" : "Not Verified"}
                </span>
              </div>

              {person.hasDIN && (
                <div className="flex items-center">
                  <span
                    className={`w-3 h-3 rounded-full mr-2 ${
                      person.dinVerified ? "bg-green-500" : "bg-red-500"
                    }`}
                  ></span>
                  <span className="text-sm">
                    DIN {person.dinVerified ? "Verified" : "Not Verified"}
                  </span>
                </div>
              )}

              <div className="flex items-center">
                <span
                  className={`w-3 h-3 rounded-full mr-2 ${
                    person.electricityVerified ? "bg-green-500" : "bg-red-500"
                  }`}
                ></span>
                <span className="text-sm">
                  Electricity{" "}
                  {person.electricityVerified ? "Verified" : "Not Verified"}
                </span>
              </div>
            </div>
          </div>

          {/* Documents */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold text-gray-900 border-b pb-2">
              Documents
            </h4>

            {documentUrls.length > 0 ? (
              <div className="space-y-2">
                {documentUrls.map((doc, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{doc.name}</span>
                    <button
                      onClick={() => handleView(doc.url!)}
                      className="text-blue-600 hover:text-blue-800 text-xs underline"
                    >
                      📄 View
                    </button>
                  </div>
                ))}

                <button
                  onClick={handleDownloadAllDocuments}
                  className="w-full mt-3 bg-green-600 text-white px-3 py-2 rounded-md text-sm hover:bg-green-700 transition-colors"
                >
                  📦 Download All as ZIP
                </button>
              </div>
            ) : (
              <p className="text-sm text-gray-500">No documents uploaded</p>
            )}
          </div>
        </div>

        {/* Timestamps */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Created At
              </label>
              <p className="text-gray-900 text-sm">
                {new Date(person.createdAt).toLocaleString()}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">
                Last Updated
              </label>
              <p className="text-gray-900 text-sm">
                {new Date(person.updatedAt).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
