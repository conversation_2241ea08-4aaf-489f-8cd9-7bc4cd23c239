'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface Application {
  id: string;
  companyName: string;
  companyType: string;
  status: 'IN_PROGRESS' | 'SUBMITTED' | 'DOCUMENTS_PENDING' | 'COMPLETED';
  createdAt: string;
  updatedAt: string;
  user: {
    firstName: string;
    middleName?: string;
    lastName?: string;
    email?: string;
    contact?: string;
  };
}

export default function ApplicationsSection() {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'in-progress' | 'document-needed' | 'completed'>('all');

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get('/api/admin/applications?filter=all');
        setApplications(response.data.applications);
      } catch (error) {
        console.error('Error fetching applications:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUBMITTED':
      case 'DOCUMENTS_PENDING':
        return 'bg-orange-100 text-orange-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return 'In Progress';
      case 'SUBMITTED':
        return 'Document Needed';
      case 'DOCUMENTS_PENDING':
        return 'Document Needed';
      case 'COMPLETED':
        return 'Completed';
      default:
        return status;
    }
  };

  const filteredApplications = applications.filter(app => {
    // First filter by status
    let statusMatch = true;
    if (statusFilter === 'in-progress') {
      statusMatch = app.status === 'IN_PROGRESS';
    } else if (statusFilter === 'document-needed') {
      statusMatch = app.status === 'SUBMITTED' || app.status === 'DOCUMENTS_PENDING';
    } else if (statusFilter === 'completed') {
      statusMatch = app.status === 'COMPLETED';
    }

    // Then filter by search term
    const companyName = app.companyName?.toLowerCase() || '';
    const userName = `${app.user.firstName} ${app.user.middleName || ''} ${app.user.lastName || ''}`.toLowerCase();
    const email = app.user.email?.toLowerCase() || '';
    const search = searchTerm.toLowerCase();
    const searchMatch = companyName.includes(search) || userName.includes(search) || email.includes(search);

    return statusMatch && searchMatch;
  });

  const getStatusCounts = () => {
    return {
      all: applications.length,
      inProgress: applications.filter(app => app.status === 'IN_PROGRESS').length,
      documentNeeded: applications.filter(app => app.status === 'SUBMITTED' || app.status === 'DOCUMENTS_PENDING').length,
      completed: applications.filter(app => app.status === 'COMPLETED').length
    };
  };

  const statusCounts = getStatusCounts();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Applications Management</h2>
          <p className="text-gray-600">Total {applications.length} applications</p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setStatusFilter('all')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              statusFilter === 'all'
                ? 'bg-white text-blue-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            All ({statusCounts.all})
          </button>
          <button
            onClick={() => setStatusFilter('in-progress')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              statusFilter === 'in-progress'
                ? 'bg-white text-yellow-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            ⏳ In Progress ({statusCounts.inProgress})
          </button>
          <button
            onClick={() => setStatusFilter('document-needed')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              statusFilter === 'document-needed'
                ? 'bg-white text-orange-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            📄 Document Needed ({statusCounts.documentNeeded})
          </button>
          <button
            onClick={() => setStatusFilter('completed')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              statusFilter === 'completed'
                ? 'bg-white text-green-700 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            ✅ Completed ({statusCounts.completed})
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">
            Showing {filteredApplications.length} of {applications.length} applications
          </span>
        </div>
        <input
          type="text"
          placeholder="Search by company name, user name, or email..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Applications Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredApplications.map((app) => (
                <tr key={app.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {app.companyName || 'Untitled Company'}
                    </div>
                    <div className="text-sm text-gray-500">ID: {app.id.slice(0, 8)}...</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {app.user.firstName} {app.user.middleName} {app.user.lastName}
                      </div>
                      <div className="text-sm text-gray-500">{app.user.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {app.companyType || 'N/A'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>
                      {getStatusText(app.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(app.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => window.open(`/admin/application-detail/${app.id}`, '_blank')}
                      className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition-colors"
                    >
                      View Detail
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredApplications.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No applications found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}
