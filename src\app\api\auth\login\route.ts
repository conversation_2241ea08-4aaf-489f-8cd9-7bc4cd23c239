import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createOTP, displayOTP } from '@/lib/otp';

export async function POST(request: NextRequest) {
  try {
    const { identifier } = await request.json(); // email or contact

    // Validation
    if (!identifier) {
      return NextResponse.json(
        { error: 'Email or contact is required' },
        { status: 400 }
      );
    }

    // Find existing user
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: identifier },
          { contact: identifier },
        ],
      },
    });

    // User must exist - no auto-creation for login
    if (!user) {
      return NextResponse.json(
        { error: 'User not found. Please sign up first.' },
        { status: 404 }
      );
    }

    // Check if user has USER role
    if (user.role !== 'USER') {
      return NextResponse.json(
        { error: 'Invalid login credentials' },
        { status: 401 }
      );
    }

    // Generate OTP
    const otpCode = await createOTP(user.id);

    // In development, display OTP in console
    const otpMessage = displayOTP(otpCode);

    return NextResponse.json({
      message: 'OTP sent successfully',
      userId: user.id,
      otpMessage: process.env.NODE_ENV === 'development' ? otpMessage : undefined,
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
