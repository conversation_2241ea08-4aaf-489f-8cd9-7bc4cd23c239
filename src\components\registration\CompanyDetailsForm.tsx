"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import axios from "axios";
import {
  useRegistrationStore,
  useCompanyData,
} from "@/store/registrationStore";

interface CompanyType {
  id: string;
  name: string;
  description: string;
  minCapital: string;
  icon: string;
}

interface RegistrationFormData {
  firstName: string;
  middleName: string;
  lastName: string;
  email: string;
  phone: string;
  state: string;
  companyName: string;
  alternateName: string;
  businessKeywords: string;
  generatedObjective: string;
}

interface CompanyDetailsFormProps {
  selectedCompanyType: CompanyType | null;
  onSubmitSuccess: (registrationId: string) => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  onChangeCompanyType?: () => void;
}

const indianStates = [
  "Andhra Pradesh",
  "Arunachal Pradesh",
  "Assam",
  "Bihar",
  "Chhattisgarh",
  "Goa",
  "Gujarat",
  "Haryana",
  "Himachal Pradesh",
  "Jharkhand",
  "Karnataka",
  "Kerala",
  "Madhya Pradesh",
  "Maharashtra",
  "Manipur",
  "Meghalaya",
  "Mizoram",
  "Nagaland",
  "Odisha",
  "Punjab",
  "Rajasthan",
  "Sikkim",
  "Tamil Nadu",
  "Telangana",
  "Tripura",
  "Uttar Pradesh",
  "Uttarakhand",
  "West Bengal",
  "Delhi",
  "Jammu and Kashmir",
  "Ladakh",
  "Lakshadweep",
  "Puducherry",
];

export default function CompanyDetailsForm({
  selectedCompanyType,
  onSubmitSuccess,
  loading,
  setLoading,
  onChangeCompanyType,
}: CompanyDetailsFormProps) {
  // Zustand store integration
  const {
    companyName,
    alternativeName,
    businessKeywords,
    companyObjectives,
    companyState,
    setCompanyName,
    setAlternativeName,
    setBusinessKeywords,
    setCompanyObjectives,
    setCompanyState,
  } = useCompanyData();

  const { updateLastSaved } = useRegistrationStore();

  const [companyNameStatus, setCompanyNameStatus] = useState<
    "idle" | "checking" | "available" | "unavailable"
  >("idle");
  const [alternateNameStatus, setAlternateNameStatus] = useState<
    "idle" | "checking" | "available" | "unavailable"
  >("idle");
  const [nameSuggestions, setNameSuggestions] = useState<string[]>([]);
  const [alternateNameSuggestions, setAlternateNameSuggestions] = useState<
    string[]
  >([]);
  const [objectiveGenerating, setObjectiveGenerating] = useState(false);
  const [dataSyncing, setDataSyncing] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<RegistrationFormData>({
    defaultValues: {
      companyName: companyName || "",
      alternateName: alternativeName || "",
      businessKeywords: businessKeywords || "",
      state: companyState || "",
      generatedObjective: companyObjectives.join(", ") || "",
    },
  });

  const watchedCompanyName = watch("companyName");
  const watchedAlternateName = watch("alternateName");
  const watchedKeywords = watch("businessKeywords");
  const watchedState = watch("state");
  const watchedObjective = watch("generatedObjective");

  // Sync form changes to Zustand store
  useEffect(() => {
    if (watchedCompanyName && watchedCompanyName !== companyName) {
      setDataSyncing(true);
      setCompanyName(watchedCompanyName);
      updateLastSaved();
      setTimeout(() => setDataSyncing(false), 500);
    }
  }, [watchedCompanyName, companyName, setCompanyName, updateLastSaved]);

  useEffect(() => {
    if (watchedState && watchedState !== companyState) {
      setDataSyncing(true);
      setCompanyState(watchedState);
      updateLastSaved();
      setTimeout(() => setDataSyncing(false), 500);
    }
  }, [watchedState, companyState, setCompanyState, updateLastSaved]);

  useEffect(() => {
    if (watchedAlternateName !== alternativeName) {
      setDataSyncing(true);
      setAlternativeName(watchedAlternateName);
      updateLastSaved();
      setTimeout(() => setDataSyncing(false), 500);
    }
  }, [
    watchedAlternateName,
    alternativeName,
    setAlternativeName,
    updateLastSaved,
  ]);

  useEffect(() => {
    if (watchedKeywords !== businessKeywords) {
      setDataSyncing(true);
      setBusinessKeywords(watchedKeywords);
      updateLastSaved();
      setTimeout(() => setDataSyncing(false), 500);
    }
  }, [watchedKeywords, businessKeywords, setBusinessKeywords, updateLastSaved]);

  useEffect(() => {
    if (watchedObjective && watchedObjective !== companyObjectives.join(", ")) {
      setDataSyncing(true);
      setCompanyObjectives([watchedObjective]);
      updateLastSaved();
      setTimeout(() => setDataSyncing(false), 500);
    }
  }, [
    watchedObjective,
    companyObjectives,
    setCompanyObjectives,
    updateLastSaved,
  ]);

  const checkAuthAndLoadData = useCallback(async () => {
    try {
      const response = await axios.get("/api/auth/me");
      const userData = response.data.user;

      // Auto-fill user details
      setValue("firstName", userData.firstName);
      setValue("middleName", userData.middleName || "");
      setValue("lastName", userData.lastName || "");
      setValue("email", userData.email || "");
      setValue("phone", userData.contact || "");
    } catch (err) {
      console.error("Auth check failed:", err);
    }
  }, [setValue]);

  useEffect(() => {
    checkAuthAndLoadData();
  }, [checkAuthAndLoadData]);

  // Load Zustand data into form on mount
  useEffect(() => {
    if (companyName) {
      setValue("companyName", companyName);
    }
    if (alternativeName) {
      setValue("alternateName", alternativeName);
    }
    if (businessKeywords) {
      setValue("businessKeywords", businessKeywords);
    }
    if (companyState) {
      setValue("state", companyState);
    }
    if (companyObjectives.length > 0) {
      setValue("generatedObjective", companyObjectives.join(", "));
    }
  }, [
    companyName,
    alternativeName,
    businessKeywords,
    companyState,
    companyObjectives,
    setValue,
  ]);

  const checkNameAvailability = async (name: string, isAlternate = false) => {
    if (!name.trim()) return;

    const setStatus = isAlternate
      ? setAlternateNameStatus
      : setCompanyNameStatus;
    setStatus("checking");

    try {
      // This calls your Next.js API route, which then calls SurePass
      const response = await axios.post("/api/check-company-name", { name });
      const { available, suggestions } = response.data; // 'available' means NO matches from SurePass. 'suggestions' are the MATCHES found by SurePass.

      if (available) {
        // If 'available' is true, it means SurePass found NO companies matching the name.
        // So, the name is likely available for registration.
        setStatus("available"); // When available, there are no "suggestions" (i.e., existing names) to show.
        if (isAlternate) {
          setAlternateNameSuggestions([]);
        } else {
          setNameSuggestions([]);
        }
      } else {
        // If 'available' is false, it means SurePass FOUND one or more companies.
        // So, this name is "unavailable" (as it already exists).
        setStatus("unavailable"); // The 'suggestions' array now contains the names of the companies that were found. // You can display these as "suggestions" of existing similar names.
        if (isAlternate) {
          setAlternateNameSuggestions(suggestions || []);
        } else {
          setNameSuggestions(suggestions || []);
        }
      }
    } catch (error) {
      console.error("Error checking name availability:", error);
      setStatus("unavailable"); // Treat any error as unavailable or "cannot check" // Optionally clear suggestions on error or show a specific error message
      if (isAlternate) {
        setAlternateNameSuggestions([]);
      } else {
        setNameSuggestions([]);
      }
    }
  };

  const generateObjective = async () => {
    if (!watchedKeywords?.trim()) return;

    setObjectiveGenerating(true);
    try {
      const response = await axios.post("/api/generate-objective", {
        keywords: watchedKeywords,
        companyType: selectedCompanyType?.name,
      });

      setValue("generatedObjective", response.data.objective);
    } catch (error) {
      console.error("Error generating objective:", error);
      alert("Failed to generate objective. Please try again.");
    } finally {
      setObjectiveGenerating(false);
    }
  };

  const onSubmit = async (data: RegistrationFormData) => {
    try {
      setLoading(true);

      // Prepare data for API
      const personDetailData = {
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        state: data.state,
        companyName: data.companyName,
        alternateName: data.alternateName,
        businessKeywords: data.businessKeywords,
        generatedObjective: data.generatedObjective,
        companyType: selectedCompanyType?.name || null,
      };

      const response = await axios.post(
        "/api/save-person-details",
        personDetailData
      );

      if (response.data.success) {
        console.log(
          "✅ Basic company details saved successfully! Proceeding to next step..."
        );
        onSubmitSuccess(response.data.data.id);
      } else {
        throw new Error(response.data.error || "Failed to save data");
      }
    } catch (error) {
      console.error("Error saving basic company details:", error);
      console.log("❌ Failed to save basic company details. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
      <div className="text-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Let's Start with Basic Company Information
        </h1>
        {selectedCompanyType && (
          <div className="flex items-center justify-center space-x-4 text-blue-600">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">{selectedCompanyType.icon}</span>
              <span className="font-medium">{selectedCompanyType.name}</span>
            </div>
            {onChangeCompanyType && (
              <button
                type="button"
                onClick={onChangeCompanyType}
                className="text-sm text-gray-500 hover:text-blue-600 underline"
              >
                Change
              </button>
            )}
          </div>
        )}

        {/* Data Persistence Indicator */}
        {(companyName || companyState || companyObjectives.length > 0) && (
          <div className="mt-3">
            <span
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                dataSyncing
                  ? "bg-blue-100 text-blue-800"
                  : "bg-green-100 text-green-800"
              }`}
            >
              {dataSyncing
                ? "🔄 Saving..."
                : "✅ Company details saved automatically"}
            </span>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Auto-filled User Details */}
        <div className="bg-green-50 border border-green-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
            <span className="mr-2">✅</span>
            Auto-filled User Details (Editable)
          </h3>

          {/* Name Fields */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <input
                {...register("firstName", {
                  required: "First name is required",
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                placeholder="Edit your first name"
              />
              {errors.firstName && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.firstName.message}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Middle Name
              </label>
              <input
                {...register("middleName")}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                placeholder="Edit your middle name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name
              </label>
              <input
                {...register("lastName")}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                placeholder="Edit your last name"
              />
            </div>
          </div>

          {/* Contact Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                {...register("email")}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                placeholder="Edit your email"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <input
                {...register("phone")}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                placeholder="Edit your phone number"
              />
            </div>
          </div>

          <p className="text-sm text-green-700 mt-4">
            💡 These details are auto-filled from your account. You can edit
            them if needed.
          </p>
        </div>

        {/* State Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <span className="mr-2">🗺️</span>
            Select State
          </label>
          <select
            {...register("state", { required: "State is required" })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select a state</option>
            {indianStates.map((state) => (
              <option key={state} value={state}>
                {state}
              </option>
            ))}
          </select>
          {errors.state && (
            <p className="mt-1 text-sm text-red-600">{errors.state.message}</p>
          )}
        </div>

        {/* Company Name Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <span className="mr-2">🏢</span>
            Company Name Input
          </label>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Primary Name Field */}
            <div>
              <div className="flex">
                <input
                  {...register("companyName", {
                    required: "Company name is required",
                  })}
                  placeholder="Enter Proposed Company Name"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="button"
                  onClick={() => checkNameAvailability(watchedCompanyName)}
                  disabled={
                    !watchedCompanyName || companyNameStatus === "checking"
                  }
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 disabled:bg-gray-400 text-sm font-medium"
                >
                  {companyNameStatus === "checking"
                    ? "Checking..."
                    : "Check Availability"}
                </button>
              </div>

              {errors.companyName && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.companyName.message}
                </p>
              )}

              {companyNameStatus === "available" && (
                <p className="mt-1 text-sm text-green-600 flex items-center">
                  <span className="mr-1">✅</span>
                  Name is available!
                </p>
              )}
              {companyNameStatus === "unavailable" && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <span className="mr-1">❗</span>
                  Name not available
                </p>
              )}
            </div>

            {/* Alternate Name Field */}
            <div>
              <div className="flex">
                <input
                  {...register("alternateName", {
                    required: "Alternate name is required",
                  })}
                  placeholder="Alternate Name"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="button"
                  onClick={() =>
                    checkNameAvailability(watchedAlternateName, true)
                  }
                  disabled={
                    !watchedAlternateName || alternateNameStatus === "checking"
                  }
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 disabled:bg-gray-400 text-sm font-medium"
                >
                  {alternateNameStatus === "checking"
                    ? "Checking..."
                    : "Check Availability"}
                </button>
              </div>

              {errors.alternateName && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.alternateName.message}
                </p>
              )}

              {alternateNameStatus === "available" && (
                <p className="mt-1 text-sm text-green-600 flex items-center">
                  <span className="mr-1">✅</span>
                  Name is available!
                </p>
              )}
              {alternateNameStatus === "unavailable" && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <span className="mr-1">❗</span>
                  Name not available
                </p>
              )}
            </div>
          </div>

          {/* Company Name Suggestions */}
          {nameSuggestions.length > 0 && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm font-medium text-yellow-800 mb-2">
                Here are 3 alternate suggestions for Company Name:
              </p>
              <div className="flex flex-wrap gap-2">
                {nameSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => setValue("companyName", suggestion)}
                    className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm hover:bg-yellow-200 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Alternate Name Suggestions */}
          {alternateNameSuggestions.length > 0 && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm font-medium text-blue-800 mb-2">
                Here are 3 alternate suggestions for Alternate Name:
              </p>
              <div className="flex flex-wrap gap-2">
                {alternateNameSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => setValue("alternateName", suggestion)}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm hover:bg-blue-200 transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Company Objective */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <span className="mr-2">🧾</span>
            Company Objective (Auto Generate)
          </label>
          <div className="space-y-4">
            <div className="flex">
              <input
                {...register("businessKeywords", {
                  required: "Business keywords are required",
                })}
                placeholder="Enter 2-3 keywords about your business (e.g., software, consulting, retail)"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {!watch("generatedObjective") ? (
                <button
                  type="button"
                  onClick={generateObjective}
                  disabled={!watchedKeywords || objectiveGenerating}
                  className="px-4 py-2 bg-green-600 text-white rounded-r-lg hover:bg-green-700 disabled:bg-gray-400 font-medium"
                >
                  {objectiveGenerating ? "Generating..." : "Generate Objective"}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={generateObjective}
                  disabled={!watchedKeywords || objectiveGenerating}
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 disabled:bg-gray-400 font-medium"
                >
                  {objectiveGenerating ? "Regenerating..." : "Regenerate"}
                </button>
              )}
            </div>
            {errors.businessKeywords && (
              <p className="text-sm text-red-600">
                {errors.businessKeywords.message}
              </p>
            )}

            <textarea
              {...register("generatedObjective")}
              placeholder="Generated objective will appear here..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            />
            <p className="text-sm text-gray-600 mt-1">
              💡 You can edit the generated objective to better match your
              business needs.
            </p>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-600">Step 1 of 4: Company Details</p>
          <button
            type="submit"
            disabled={loading}
            className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium shadow-lg hover:shadow-xl transition-all flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              "Save & Next"
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
