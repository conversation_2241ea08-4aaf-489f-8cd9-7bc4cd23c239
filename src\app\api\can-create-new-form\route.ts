import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has any existing registrations
    const existingRegistrations = await prisma.basicCompanyDetailStep1.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // If no registrations exist, user can create new form
    if (existingRegistrations.length === 0) {
      return NextResponse.json({
        success: true,
        canCreateNew: true,
        message: 'No existing registrations found. You can create a new form.'
      });
    }

    // Check if latest registration is completed
    const latestRegistration = existingRegistrations[0];
    
    if (latestRegistration.status === 'COMPLETED') {
      return NextResponse.json({
        success: true,
        canCreateNew: true,
        message: 'Previous registration completed. You can create a new form.',
        previousRegistration: {
          id: latestRegistration.id,
          companyName: latestRegistration.companyName,
          status: latestRegistration.status,
          completedAt: latestRegistration.updatedAt
        }
      });
    } else {
      // User has incomplete registration
      return NextResponse.json({
        success: true,
        canCreateNew: false,
        message: 'You have an incomplete registration. Please complete it before creating a new one.',
        incompleteRegistration: {
          id: latestRegistration.id,
          companyName: latestRegistration.companyName,
          status: latestRegistration.status,
          createdAt: latestRegistration.createdAt,
          updatedAt: latestRegistration.updatedAt
        }
      });
    }

  } catch (error) {
    console.error('Error checking form creation eligibility:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
