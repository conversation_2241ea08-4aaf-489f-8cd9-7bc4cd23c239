'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import Navbar from '@/components/Navbar';
import Footer from '@/components/footer';

interface User {
  id: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  role: 'USER';
}

interface Registration {
  id: string;
  companyName: string;
  companyType: string;
  status: 'IN_PROGRESS' | 'SUBMITTED' | 'DOCUMENTS_PENDING' | 'COMPLETED';
  createdAt: string;
  updatedAt: string;
}

export default function Dashboard() {
  const [user, setUser] = useState<User | null>(null);
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(true);
  const [canCreateNew, setCanCreateNew] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        console.log('Dashboard: Starting to load data...');

        // Get user data first
        const userResponse = await axios.get('/api/auth/me');
        console.log('Dashboard: User response:', userResponse.data);

        if (!userResponse.data.user) {
          console.error('No user data received');
          router.push('/login');
          return;
        }
        setUser(userResponse.data.user);

        // Get user's registrations (don't fail if this fails)
        try {
          const registrationsResponse = await axios.get('/api/get-user-registrations');
          setRegistrations(registrationsResponse.data.registrations || []);
        } catch (regError) {
          console.error('Error loading registrations:', regError);
          setRegistrations([]); // Set empty array instead of failing
        }

        // Check if user can create new form (don't fail if this fails)
        try {
          const canCreateResponse = await axios.get('/api/can-create-new-form');
          setCanCreateNew(canCreateResponse.data.canCreateNew);
        } catch (canCreateError) {
          console.error('Error checking can create new:', canCreateError);
          setCanCreateNew(true); // Default to allowing creation
        }

      } catch (error) {
        console.error('Dashboard: Error loading user data:', error);
        console.error('Dashboard: Error details:', {
          isAxiosError: axios.isAxiosError(error),
          status: axios.isAxiosError(error) ? error.response?.status : 'N/A',
          message: error instanceof Error ? error.message : 'Unknown error'
        });

        // Set error message instead of redirecting for debugging
        const errorMessage = axios.isAxiosError(error)
          ? `API Error: ${error.response?.status} - ${error.response?.data?.error || error.message}`
          : `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;

        setError(errorMessage);

        // Only redirect to login if it's definitely an auth error
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          console.log('Dashboard: 401 error, redirecting to login');
          setTimeout(() => router.push('/login'), 3000); // Delay to show error first
        }
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [router]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUBMITTED':
      case 'DOCUMENTS_PENDING':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return '⏳ In Progress';
      case 'SUBMITTED':
        return '📝 Documents Pending';
      case 'DOCUMENTS_PENDING':
        return '📝 Documents Pending';
      case 'COMPLETED':
        return '✅ Completed';
      default:
        return status;
    }
  };

  const handleContinueRegistration = (registration: Registration) => {
    // Store the registration ID to continue with
    localStorage.setItem('registrationId', registration.id);

    // Always go to register page - it will determine the correct step based on status
    router.push('/register');
  };

  const handleCreateNewForm = async () => {
    try {
      const response = await axios.get('/api/can-create-new-form');
      if (response.data.canCreateNew) {
        // Clear any existing registration data and start fresh
        localStorage.removeItem('selectedCompanyType');
        localStorage.removeItem('registrationId');
        // Clear Zustand store as well
        window.location.href = '/register'; // Force full page reload to clear Zustand
      } else {
        alert('Please complete your current registration before creating a new one.');
      }
    } catch (error) {
      console.error('Error checking form creation eligibility:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
            <h3 className="text-lg font-semibold text-red-800 mb-2">Dashboard Error</h3>
            <p className="text-red-700 text-sm mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
        
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />
      
      <div className="flex-1 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {user?.firstName} {user?.lastName || ''}!
            </h1>
            <p className="text-gray-600 mt-2">
              Manage your company registrations and track their progress.
              
            </p>
            
          </div>
          {registrations.length > 0 && (
  <div className={`flex items-center gap-2 px-4 py-3 mt-4 mb-8 rounded-md text-sm font-medium shadow-sm ${getStatusColor(registrations[0].status)}`}>
    <span className="text-lg">📢</span>
    <span>
      Your  application for (<strong>{registrations[0].companyName || 'Untitled Company'}</strong>) company is <strong>{getStatusText(registrations[0].status)}</strong>.
    </span>
  </div>
)}


          

          {/* Applications Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {registrations.length === 0 ? (
              <div className="col-span-full bg-white rounded-lg shadow p-8 text-center">
                <div className="text-gray-400 text-6xl mb-4">📋</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Registrations Yet</h3>
                <p className="text-gray-600 mb-4">
                  Start your first company registration to see it here.
                </p>
                <button
                  onClick={handleCreateNewForm}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                >
                  Start Registration
                </button>
              </div>
            ) : (
              registrations.map((registration) => (
                <div key={registration.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {registration.companyName || 'Untitled Company'}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(registration.status)}`}>
                        {getStatusText(registration.status)}
                      </span>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Type:</span> {registration.companyType || 'Not specified'}
                      </p>
                      <p className="text-sm text-gray-600">
                        <span className="font-medium">Created:</span> {new Date(registration.createdAt).toLocaleDateString()}
                      </p>
                      
                    </div>

                    <div className="flex space-x-2">
                      {registration.status !== 'COMPLETED' && (
                        <button
                          onClick={() => handleContinueRegistration(registration)}
                          className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                        >
                          Continue
                        </button>
                      )}
                      <button
                        onClick={() => {/* View details */}}
                        className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
