'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface CompanyData {
  // Basic Company Details
  companyName: string;
  companyType: string;
  state: string;
  alternateName?: string;
  businessKeywords: string;
  generatedObjective?: string;

  // Address & Capital Details
  companyAddress: string;
  isDirectorAddressSame?: boolean;
  addressProofType?: string;
  shareCapital: number;
  shareRatios: Array<{
    personIndex: number;
    personName: string;
    personRole: string;
    sharePercentage: number;
  }>;
}

interface PersonData {
  personIndex: number;
  personRole: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email: string;
  contact: string;
  aadhaarVerified: boolean;
  aadhaarData?: any;
  panVerified: boolean;
  panData?: any;
  hasDIN?: boolean;
  dinVerified: boolean;

  electricityVerified?: boolean;
  isCompleted: boolean;
}

interface RegistrationReviewProps {
  registrationId: string;
}

export default function RegistrationReview({ registrationId }: RegistrationReviewProps) {
  const [loading, setLoading] = useState(true);
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [personsData, setPersonsData] = useState<PersonData[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReviewData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch company data
        try {
          const companyResponse = await axios.get(`/api/get-company-details?registrationId=${registrationId}`);
          if (companyResponse.data.success) {
            setCompanyData(companyResponse.data.data);
          }
        } catch (companyError) {
          console.error('Error fetching company data:', companyError);
          // Continue to fetch person data even if company data fails
        }

        // Fetch persons data
        try {
          const personsResponse = await axios.get(`/api/get-person-details?registrationId=${registrationId}`);
          if (personsResponse.data.success) {
            setPersonsData(personsResponse.data.data);
          }
        } catch (personsError) {
          console.error('Error fetching persons data:', personsError);
          // Set error only if both requests fail
          if (!companyData) {
            throw personsError;
          }
        }

      } catch (err: any) {
        console.error('Error fetching review data:', err);
        setError(err.response?.data?.error || 'Failed to fetch registration data');
      } finally {
        setLoading(false);
      }
    };

    if (registrationId) {
      fetchReviewData();
    }
  }, [registrationId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading registration data...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center space-x-3">
          <span className="text-red-600 text-xl">❌</span>
          <div>
            <h3 className="text-red-800 font-medium">Error Loading Data</h3>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Step 1: Company Details */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <span className="text-green-600 font-semibold text-sm">1</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Company Details</h3>
        </div>

        {companyData ? (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{companyData.companyName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company Type</label>
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{companyData.companyType}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{companyData.state}</p>
              </div>
              {companyData.alternateName && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Alternate Name</label>
                  <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{companyData.alternateName}</p>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Business Keywords</label>
              <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{companyData.businessKeywords}</p>
            </div>

            {companyData.generatedObjective && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Generated Objective</label>
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg text-sm leading-relaxed">
                  {companyData.generatedObjective}
                </p>
              </div>
            )}
          </div>
        ) : (
          <p className="text-gray-500 italic">No company data found</p>
        )}
      </div>

      {/* Step 2: Person Details */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <span className="text-green-600 font-semibold text-sm">2</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Person Details</h3>
        </div>

        {personsData.length > 0 ? (
          <div className="space-y-6">
            {personsData.map((person, index) => (
              <div key={person.personIndex} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">{person.personRole}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    person.isCompleted 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {person.isCompleted ? '✅ Completed' : '⏳ Pending'}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg text-sm">
                      {`${person.firstName} ${person.middleName || ''} ${person.lastName || ''}`.trim()}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg text-sm">{person.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Contact</label>
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg text-sm">{person.contact}</p>
                  </div>
                </div>

                {/* Verification Status */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Verification Status</label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <div className="flex items-center space-x-2">
                      <span className={`w-3 h-3 rounded-full ${person.aadhaarVerified ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span className="text-sm text-gray-700">Aadhaar</span>
                      {person.aadhaarData?.aadhaarNumber && (
                        <span className="text-xs text-gray-500">({person.aadhaarData.aadhaarNumber})</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`w-3 h-3 rounded-full ${person.panVerified ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span className="text-sm text-gray-700">PAN</span>
                      {person.panData?.panNumber && (
                        <span className="text-xs text-gray-500">({person.panData.panNumber})</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`w-3 h-3 rounded-full ${person.dinVerified ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span className="text-sm text-gray-700">DIN</span>
                      {!person.hasDIN && (
                        <span className="text-xs text-gray-500">(N/A)</span>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className={`w-3 h-3 rounded-full ${person.electricityVerified ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span className="text-sm text-gray-700">Address Verification</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 italic">No person data found</p>
        )}
      </div>

      {/* Step 3: Address & Capital */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <span className="text-green-600 font-semibold text-sm">3</span>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Address & Capital</h3>
        </div>

        {companyData ? (
          <div className="space-y-6">
            {/* Address Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Director Address Same as Company</label>
                <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                  {companyData.isDirectorAddressSame === true ? 'Yes' :
                   companyData.isDirectorAddressSame === false ? 'No' : 'Not specified'}
                </p>
              </div>
              {companyData.addressProofType && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address Proof Type</label>
                  <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{companyData.addressProofType}</p>
                </div>
              )}
            </div>

            {/* Share Capital */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Total Share Capital</label>
              <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">₹{companyData.shareCapital?.toLocaleString()}</p>
            </div>

            {/* Share Ratio Allocation */}
            {companyData.shareRatios && companyData.shareRatios.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Share Ratio Allocation</label>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-3">
                    {companyData.shareRatios.map((ratio, index) => (
                      <div key={ratio.personIndex} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 text-xs font-medium">{ratio.personIndex + 1}</span>
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{ratio.personName}</p>
                            <p className="text-sm text-gray-600">{ratio.personRole}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">{ratio.sharePercentage}%</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-700">Total Allocation:</span>
                      <span className={`font-semibold ${
                        companyData.shareRatios.reduce((sum, ratio) => sum + ratio.sharePercentage, 0) === 100
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {companyData.shareRatios.reduce((sum, ratio) => sum + ratio.sharePercentage, 0)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <p className="text-gray-500 italic">No address & capital data found</p>
        )}
      </div>

      {/* Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-4">📋 Registration Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{companyData ? '1' : '0'}</div>
            <div className="text-blue-700">Company Registered</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{personsData.length}</div>
            <div className="text-blue-700">Persons Added</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {personsData.filter(p => p.isCompleted).length}/{personsData.length}
            </div>
            <div className="text-blue-700">Persons Completed</div>
          </div>
        </div>
      </div>
    </div>
  );
}
