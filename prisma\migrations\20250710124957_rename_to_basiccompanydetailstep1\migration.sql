/*
  Warnings:

  - You are about to drop the `PersonDetail` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "PersonDetail" DROP CONSTRAINT "PersonDetail_userId_fkey";

-- DropTable
DROP TABLE "PersonDetail";

-- CreateTable
CREATE TABLE "basicCompanyDetailStep1" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "middleName" TEXT,
    "lastName" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "state" TEXT NOT NULL,
    "companyName" TEXT NOT NULL,
    "alternateName" TEXT,
    "businessKeywords" TEXT NOT NULL,
    "generatedObjective" TEXT,
    "companyType" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "basicCompanyDetailStep1_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "basicCompanyDetailStep1_userId_idx" ON "basicCompanyDetailStep1"("userId");

-- AddForeignKey
ALTER TABLE "basicCompanyDetailStep1" ADD CONSTRAINT "basicCompanyDetailStep1_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
