'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import OTPForm from '@/components/OTPForm';
import Navbar from '@/components/Navbar';
import Footer from '@/components/footer';

function OTPPageContent() {
  const searchParams = useSearchParams();
  const identifier = searchParams.get('identifier') || '';
  const userType = (searchParams.get('type') as 'user' | 'admin') || 'user';

  if (!identifier) {
    return (
      <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
        <h2 className="text-2xl font-bold mb-6 text-center text-red-600">Error</h2>
        <p className="text-center text-gray-600">
          Invalid verification link. Please try logging in again.
        </p>
        <div className="mt-4 text-center">
          <a href="/login" className="text-blue-600 hover:text-blue-500">
            Back to Login
          </a>
        </div>
      </div>
    );
  }

  return <OTPForm identifier={identifier} userType={userType} />;
}

export default function VerifyOTPPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />
      <div className="container mx-auto px-4 py-8 flex-1">
        <Suspense fallback={<div>Loading...</div>}>
          <OTPPageContent />
        </Suspense>
      </div>
      <Footer />
    </div>
  );
}
