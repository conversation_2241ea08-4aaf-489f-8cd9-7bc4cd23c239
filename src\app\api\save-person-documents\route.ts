import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const {
      invitationId,
      registrationId,
      personIndex,
      aadhaarDocUrl,
      panDocUrl,
      photoUrl,
      signatureUrl,
      geoTagImageUrl,
      bankTransactionDetailsUrl,
      addressVerificationDocUrl,

      aadhaarDocUploaded,
      panDocUploaded,
      photoUploaded,
      signatureUploaded,
      geoTagImageUploaded,
      bankTransactionDetailsUploaded,
      addressVerificationDocUploaded,

      documentsCompleted,
    } = await request.json();

    // Validation
    if (!registrationId || personIndex === undefined) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // For Person A (no invitationId), validate that all invited persons have completed documents
    if (!invitationId) {
      console.log(
        "API: Checking if all invited persons have completed documents..."
      );

      // Get all invitations for this registration
      const invitations = await prisma.personInvitation.findMany({
        where: {
          registrationId,
        },
      });

      // REMOVED: Document submission validation for Person A
      // Person A can now upload documents without waiting for invited persons to complete their documents
      // Only form submission is required from invited persons
    }

    // Find the person detail record
    let personDetail;
    if (invitationId) {
      // If this is from an invited person
      personDetail = await prisma.personDetail.findFirst({
        where: {
          invitationId,
          registrationId,
          personIndex,
        },
      });
    } else {
      // If this is from the main user
      personDetail = await prisma.personDetail.findFirst({
        where: {
          registrationId,
          personIndex,
          invitationId: null,
        },
      });
    }

    if (!personDetail) {
      return NextResponse.json(
        { error: "Person details not found" },
        { status: 404 }
      );
    }

    // Update the person detail with document information
    const updatedPersonDetail = await prisma.personDetail.update({
      where: {
        id: personDetail.id,
      },
      data: {
        aadhaarDocUrl,
        panDocUrl,
        photoUrl,
        signatureUrl,
        geoTagImageUrl,
        bankTransactionDetailsUrl,
        addressVerificationDocUrl,

        aadhaarDocUploaded,
        panDocUploaded,
        photoUploaded,
        signatureUploaded,
        geoTagImageUploaded,
        bankTransactionDetailsUploaded,
        addressVerificationDocUploaded,

        documentsCompleted,
        documentsCompletedAt: documentsCompleted ? new Date() : null,
      },
    });

    // If this is from an invited person, update the invitation status to DOCUMENTS_SUBMITTED
    if (invitationId) {
      await prisma.personInvitation.update({
        where: {
          id: invitationId,
        },
        data: {
          status: "DOCUMENTS_SUBMITTED",
          completedAt: new Date(),
        },
      });
    }

    // Check if all persons have completed documents, then update registration status to COMPLETED
    if (documentsCompleted) {
      // Get all persons for this registration
      const allPersons = await prisma.personDetail.findMany({
        where: {
          registrationId: registrationId,
        },
      });

      // Check if all persons have completed documents
      const allDocumentsCompleted = allPersons.every(
        (person) => person.documentsCompleted
      );

      if (allDocumentsCompleted) {
        // Update registration status to COMPLETED
        await prisma.basicCompanyDetailStep1.updateMany({
          where: {
            id: registrationId,
          },
          data: {
            status: "COMPLETED",
            updatedAt: new Date(),
          },
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: "Person documents saved successfully",
      data: {
        id: updatedPersonDetail.id,
        documentsCompleted: updatedPersonDetail.documentsCompleted,
      },
    });
  } catch (error) {
    console.error("Error saving person documents:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
