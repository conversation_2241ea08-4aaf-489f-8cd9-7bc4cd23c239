{"name": "taxlegitmvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:seed": "ts-node prisma/seed.ts", "seed:company-types": "ts-node prisma/seed-company-types.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@aws-sdk/client-s3": "^3.837.0", "@aws-sdk/s3-request-presigner": "^3.837.0", "@google/generative-ai": "^0.24.1", "@prisma/client": "^6.9.0", "@types/jszip": "^3.4.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lucide-react": "^0.518.0", "next": "15.3.3", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.4", "prisma": "^6.9.0", "tailwindcss": "^4.1.8", "ts-node": "^10.9.2", "typescript": "^5"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}