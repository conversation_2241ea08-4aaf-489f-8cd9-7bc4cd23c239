import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Invite token is required' },
        { status: 400 }
      );
    }

    // Find invitation in database
    const invitation = await prisma.personInvitation.findUnique({
      where: { inviteToken: token },
      include: {
        user: true // Include inviter details
      }
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' },
        { status: 404 }
      );
    }

    // Check if invitation has expired
    if (invitation.expiresAt < new Date()) {
      return NextResponse.json(
        { error: 'Invitation has expired' },
        { status: 410 }
      );
    }

    // Check if invitation is already fully completed (documents submitted)
    if (invitation.status === 'DOCUMENTS_SUBMITTED') {
      return NextResponse.json(
        { error: 'Invitation has already been fully completed' },
        { status: 409 }
      );
    }

    // For the documents page, we need to check if form is submitted
    const isDocumentsPage = request.url.includes('/documents');
    if (isDocumentsPage && invitation.status !== 'FORM_SUBMITTED') {
      return NextResponse.json(
        { error: 'Please complete your personal details first' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: invitation.id,
        invitationId: invitation.id,
        token: invitation.inviteToken,
        registrationId: invitation.registrationId,
        personRole: invitation.personRole,
        personIndex: invitation.personIndex,
        status: invitation.status,
        inviterName: `${invitation.user.firstName} ${invitation.user.lastName || ''}`.trim(),
        inviterEmail: invitation.user.email,
        companyName: 'Company Registration', // You might want to fetch actual company name
        expiresAt: invitation.expiresAt.toISOString(),
        message: 'Please complete your details for company registration'
      }
    });

  } catch (error) {
    console.error('Error validating invitation:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
