import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const {
      invitationId,
      firstName,
      middleName,
      lastName,
      email,
      contact,
      aadhaarVerified,
      aadhaarData,
      panVerified,
      panData,
      hasDIN,
      dinVerified,
      dinData,

      electricityConnectionNumber,
      electricityBoardName,
      electricityVerified,
      electricityData
    } = await request.json();

    // Validation
    if (!invitationId || !firstName || !email || !contact) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get invitation details
    const invitation = await prisma.personInvitation.findUnique({
      where: { id: invitationId }
    });

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invalid invitation' },
        { status: 404 }
      );
    }

    // Check if invitation has expired
    if (invitation.expiresAt < new Date()) {
      return NextResponse.json(
        { error: 'Invitation has expired' },
        { status: 410 }
      );
    }

    // Save person details with proper invitation linking
    const personDetail = await prisma.personDetail.create({
      data: {
        userId: invitation.userId,
        invitationId: invitation.id,
        registrationId: invitation.registrationId,
        personIndex: invitation.personIndex,
        personRole: invitation.personRole,
        firstName,
        middleName: middleName || null,
        lastName: lastName || null,
        email,
        contact,
        aadhaarVerified: aadhaarVerified || false,
        aadhaarData: aadhaarData || null,
        panVerified: panVerified || false,
        panData: panData || null,
        hasDIN: hasDIN || false,
        dinVerified: dinVerified || false,
        dinData: dinData || null,

        electricityConnectionNumber: electricityConnectionNumber || null,
        electricityBoardName: electricityBoardName || null,
        electricityVerified: electricityVerified || false,
        electricityData: electricityData || null,
        isCompleted: true,
        completedAt: new Date()
      }
    });

    // Update invitation status to FORM_SUBMITTED (personal details completed)
    const updatedInvitation = await prisma.personInvitation.update({
      where: { id: invitation.id },
      data: {
        status: 'FORM_SUBMITTED',
        completedAt: new Date()
      }
    });

    console.log('Updated invitation status to:', updatedInvitation.status);

    return NextResponse.json({
      success: true,
      message: 'Person details saved successfully',
      data: personDetail
    });

  } catch (error) {
    console.error('Error saving invited person details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
