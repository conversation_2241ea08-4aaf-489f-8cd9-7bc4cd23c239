// pages/api/initialize-digilocker.ts

import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  // Use the same token as other APIs for consistency
  const surepassAuthToken = process.env.SUREPASS_API_TOKEN;

  if (!surepassAuthToken) {
    console.error("Surepass API token is not configured on the server.");
    return NextResponse.json(
      { success: false, error: "Internal server configuration error." },
      { status: 500 }
    );
  }

  try {
    // Call the Surepass Initialize API
    const response = await fetch(
      "https://sandbox.surepass.app/api/v1/digilocker/initialize", // Use production URL for live
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${surepassAuthToken}`,
        },
        body: JSON.stringify({
          data: {
            // <-- Yeh "data" object add karna hai
            signup_flow: true,
          },
        }),
      }
    );

    const data = await response.json();

    if (!response.ok || !data.success) {
      return NextResponse.json(
        { success: false, error: data.message || "Failed to initialize." },
        { status: response.status }
      );
    }

    // IMPORTANT: Send the token and client_id back to the frontend
    return NextResponse.json({
      success: true,
      token: data.data.token,
      clientId: data.data.client_id,
    });
  } catch (error) {
    console.error("Error in /api/initialize-digilocker:", error);
    return NextResponse.json(
      { success: false, error: "An internal server error occurred." },
      { status: 500 }
    );
  }
}
