'use client';
import axios from 'axios';
import { useEffect, useState } from 'react';
import Image from 'next/image';


interface User {
  id: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  contact?: string;
  role: 'USER' | 'ADMIN';
}



export default function Hero() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Calculate date 2 days from now
  const getRegistrationDate = () => {
    const today = new Date();
    const registrationDate = new Date(today);
    registrationDate.setDate(today.getDate() + 2);

    return registrationDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const carouselImages = [
    {
      src: 'https://plus.unsplash.com/premium_photo-1661693516373-e7df30ef9e4b?q=80&w=1033&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      alt: 'Modern office building representing company registration',
      caption: '500+ Registrations',
    },
    {
      src: 'https://images.unsplash.com/photo-1706759755789-66d39fd252b1?q=80&w=774&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      alt: 'Business documents and paperwork automation',
      caption: 'Get Registered in Few Clicks',
    },
    {
      src: 'https://images.unsplash.com/photo-1681505504714-4ded1bc247e7?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      alt: 'Team collaboration and business growth',
      caption: '24/7 Support',
    }
  ];

  useEffect(() => {
    checkAuthStatus();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselImages.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [carouselImages.length]);

  const checkAuthStatus = async () => {
    try {
      const response = await axios.get('/api/auth/me');
      setUser(response.data.user);
    } catch (err) {
      console.error('Auth check failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselImages.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselImages.length) % carouselImages.length);
  };

  const handleStartRegistration = () => {
    // Directly redirect to register page where modal will open automatically
    window.location.href = '/register';
  };

  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-16 lg:py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Half Section Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

            {/* Content Half */}
            <div className="order-2 lg:order-1">
              {!loading && user && (
                <p className="text-lg text-blue-600 font-medium mb-4">
                  Welcome back, {user.firstName} {user.lastName || ''}!
                </p>
              )}
              <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Company Registration
                <span className="text-blue-600"> Made Simple</span>
              </h1>
              <p className="text-xl text-gray-600 mb-6 leading-relaxed">
                Experience the simplest automation way to register your company.
                From documentation to compliance - we handle it all seamlessly.
                🚀 Get your company registration by{' '}
                  <span className="text-green-700 font-bold">
                    {getRegistrationDate()}
                  </span>
              </p>

             

              {/* Features */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-8">
                <div className="text-center sm:text-left">
                  <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto sm:mx-0">
                    <span className="text-xl">🤖</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 text-sm">100% Automated Process</h3>
                </div>

                <div className="text-center sm:text-left">
                  <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto sm:mx-0">
                    <span className="text-xl">🕒</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 text-sm">24/7 Support</h3>
                </div>

                <div className="text-center sm:text-left">
                  <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mb-3 mx-auto sm:mx-0">
                    <span className="text-xl">⚡</span>
                  </div>
                  <h3 className="font-semibold text-gray-900 text-sm">Fast & Secure</h3>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                {user ? (
                  <button
                    onClick={handleStartRegistration}
                    className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-lg font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
                  >
                    Start Registration
                  </button>
                ) : (
                  <a
                    href="/login"
                    className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-lg font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
                  >
                    Login to Start Registration
                  </a>
                )}
                <a
                  href="#learn-more"
                  className="inline-flex items-center justify-center px-8 py-4 border border-gray-300 text-lg font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 shadow-md hover:shadow-lg"
                >
                  Learn More
                </a>
              </div>
            </div>

            {/* Carousel Half */}
            <div className="order-1 lg:order-2">
              <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl">
                {/* Carousel Images */}
                {carouselImages.map((image, index) => (
                  <div
                    key={index}
                    className={`absolute inset-0 transition-opacity duration-1000 ${
                      index === currentSlide ? 'opacity-100' : 'opacity-0'
                    }`}
                  >
                    <Image
                      src={image.src}
                      alt={image.alt}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent"></div>

                    {/* Caption Overlay */}
                    <div className="absolute top-2 left-2 bg-blue-600 backdrop-blur-sm rounded-full p-2 shadow-lg">
                      <div className="text-sm lg:text-xl font-bold  text-white ">
                        {image.caption}
                      </div> 
                    </div>
                  </div>
                ))}

                {/* Navigation Arrows */}
                <button
                  onClick={prevSlide}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg transition-all duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg transition-all duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                {/* Dots Indicator */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {carouselImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-200 ${
                        index === currentSlide ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>


    </div>
  );
}
