'use client';

import { useState } from 'react';

interface CompanyType {
  id: string;
  name: string;
  code: string;
  description: string;
  minCapital: string;
  minShareCapital: number;
  icon: string;
}

interface CompanyTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceed: (companyType: CompanyType) => void;
}

const companyTypes: CompanyType[] = [
  {
    id: 'opc',
    name: 'One Person Company',
    code: 'OPC',
    description: 'Perfect for solo entrepreneurs who want to start their own business with limited liability.',
    minCapital: '₹1,00,000',
    minShareCapital: 100000,
    icon: '👤'
  },
  {
    id: 'pvt',
    name: 'Private Limited Company',
    code: 'PVT',
    description: 'Ideal for startups and growing businesses with multiple shareholders.',
    minCapital: '₹1,00,000',
    minShareCapital: 100000,
    icon: '🏢'
  },
  {
    id: 'llp',
    name: 'Limited Liability Partnership',
    code: 'LLP',
    description: 'Great for professional services and partnerships with operational flexibility.',
    minCapital: '₹10,000',
    minShareCapital: 10000,
    icon: '🤝'
  },
  {
    id: 'section8',
    name: 'Section-8 Company',
    code: 'SECTION8',
    description: 'For non-profit organizations focused on promoting social causes.',
    minCapital: '₹10,000',
    minShareCapital: 10000,
    icon: '🌟'
  }
];

export default function CompanyTypeModal({ isOpen, onClose, onProceed }: CompanyTypeModalProps) {
  const [selectedType, setSelectedType] = useState<CompanyType | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  if (!isOpen) return null;

  const handleTypeSelect = (type: CompanyType) => {
    setSelectedType(type);
    setShowConfirmation(true);
  };

  const handleProceed = () => {
    if (selectedType) {
      onProceed(selectedType);
      onClose();
    }
  };

  const handleBack = () => {
    setShowConfirmation(false);
    setSelectedType(null);
  };

  return (
    <div className="fixed inset-0 bg-transparent backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-t-2xl">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold mb-2">Start Your Business in Just Clicks</h2>
              <p className="text-blue-100">Select your company type to begin registration</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {!showConfirmation ? (
          /* Company Type Selection */
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {companyTypes.map((type) => (
                <div
                  key={type.id}
                  onClick={() => handleTypeSelect(type)}
                  className="border-2 border-gray-200 rounded-xl p-6 hover:border-blue-500 hover:shadow-lg transition-all duration-200 cursor-pointer group"
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl group-hover:scale-110 transition-transform duration-200">
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        {type.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3 leading-relaxed">
                        {type.description}
                      </p>
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <p className="text-sm text-green-800">
                          <span className="font-medium">Minimum Share Capital:</span> {type.minCapital}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <span className="text-blue-600 font-medium text-sm group-hover:text-blue-700">
                      Select →
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          /* Confirmation Screen */
          <div className="p-8 text-center">
            <div className="mb-6">
              <div className="text-6xl mb-4">{selectedType?.icon}</div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                {selectedType?.name}
              </h3>
              <p className="text-gray-600 mb-4">
                {selectedType?.description}
              </p>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
              <h4 className="text-lg font-semibold text-blue-900 mb-2">
                Capital Requirements
              </h4>
              <p className="text-blue-800">
                The minimum share capital for {selectedType?.name} starts from{' '}
                <span className="font-bold text-xl">{selectedType?.minCapital}</span>
              </p>
              <p className="text-blue-700 text-sm mt-2">
                Do you want to proceed with this company type?
              </p>
            </div>

            <div className="flex justify-center space-x-4">
              <button
                onClick={handleBack}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
              >
                ← Back
              </button>
              <button
                onClick={handleProceed}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium shadow-lg hover:shadow-xl"
              >
                Yes, Proceed
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
