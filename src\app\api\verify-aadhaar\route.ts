import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    // Correctly expecting 'clientId' from the frontend
    const { clientId } = await request.json();

    console.log(`🔄 [verify-aadhaar API] Received clientId:`, clientId);

    if (!clientId) {
      console.error(`❌ [verify-aadhaar API] Missing clientId`);
      return NextResponse.json(
        { success: false, error: "clientId is required" },
        { status: 400 }
      );
    }

    const apiToken = process.env.SUREPASS_API_TOKEN;

    if (!apiToken) {
      console.error("Surepass API token is not configured on the server.");
      return NextResponse.json(
        { success: false, error: "Internal server configuration error." },
        { status: 500 }
      );
    }

    // Correctly using 'clientId' to build the Surepass API URL
    const surepassApiUrl = `https://kyc-api.surepass.io/api/v1/digilocker/download-aadhaar/${clientId}`;

    console.log(
      `🌐 [verify-aadhaar API] Calling Surepass URL:`,
      surepassApiUrl
    );

    const response = await fetch(surepassApiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiToken}`,
      },
    });

    console.log(
      `📡 [verify-aadhaar API] Surepass response status:`,
      response.status
    );

    if (!response.ok) {
      const errorData = await response.json();
      console.error(`❌ [verify-aadhaar API] Surepass error:`, errorData);
      return NextResponse.json(
        {
          success: false,
          error: errorData.message || "Failed to fetch data from Surepass.",
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`✅ [verify-aadhaar API] Surepass success response:`, data);

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in /api/verify-aadhaar:", error);
    return NextResponse.json(
      { success: false, error: "An internal server error occurred." },
      { status: 500 }
    );
  }
}
