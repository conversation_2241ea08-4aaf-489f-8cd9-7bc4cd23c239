"use client";

import { useState, useEffect, useRef } from "react";
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  useWatch,
} from "react-hook-form";
import axios from "axios";

// Define the shape of the form data for type safety
interface PersonFormData {
  persons: Array<{
    firstName: string;
    middleName?: string;
    lastName?: string;
    email: string;
    contact: string;
    aadhaarNumber?: string;
    aadhaarVerified: boolean;
    aadhaarData?: unknown;
    panNumber?: string;
    panVerified: boolean;
    panData?: unknown;
    hasDIN?: boolean;
    dinNumber?: string;
    dinVerified: boolean;
    dinData?: unknown;
    electricityConnectionNumber?: string;
    electricityBoardName?: string;
    electricityVerified?: boolean;
    electricityData?: unknown;
    addressVerificationMethod?: string;
  }>;
}

// Define the shape of the Aadhaar data expected from the backend
interface AadhaarData {
  name: string;
  fatherName: string;
  gender: string;
  aadhaarNumber: string;
  address: string;
}

// Props for the AadhaarVerification component
interface AadhaarVerificationProps {
  index: number;
  control: Control<PersonFormData>;
  errors: FieldErrors<PersonFormData>;
  setValue: UseFormSetValue<PersonFormData>;
  expanded: boolean; // Not directly used in this version but kept for consistency
  onToggle: () => void; // Not directly used in this version but kept for consistency
  onShowToast?: (message: string, type: "success" | "error" | "info") => void;
}

export default function AadhaarVerification({
  index,
  control,
  errors,
  setValue,
  onShowToast,
}: AadhaarVerificationProps) {
  // State to manage the Digilocker initialization process
  const [initializingDigilocker, setInitializingDigilocker] = useState(false);
  // State to manage if the Digilocker pop-up is conceptually open
  const [digilockerPopupOpen, setDigilockerPopupOpen] = useState(false);
  // State to manage the polling for Aadhaar data after Digilocker flow
  const [pollingAadhaarData, setPollingAadhaarData] = useState(false);
  // Stores the client ID obtained from Digilocker initialization
  const [clientIdForDigilocker, setClientIdForDigilocker] = useState<
    string | null
  >(null);
  // Stores the verified Aadhaar data to display
  const [aadhaarData, setAadhaarData] = useState<AadhaarData | null>(null);
  // State to show manual Aadhaar input fallback
  const [showManualAadhaarInput, setShowManualAadhaarInput] = useState(false);
  // State for manual Aadhaar number input
  const [manualAadhaarNumber, setManualAadhaarNumber] = useState("");

  // Ref to store the polling interval ID to clear it later
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  // Ref to store the pop-up window object
  const popupRef = useRef<Window | null>(null);

  // Watch the 'aadhaarVerified' status from the form
  const verified = useWatch({
    control,
    name: `persons.${index}.aadhaarVerified`,
    defaultValue: false,
  });

  // Effect to clean up polling interval and pop-up on component unmount
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      if (popupRef.current) {
        popupRef.current.close();
      }
    };
  }, []);

  /**
   * Handles the initiation of the Digilocker verification flow.
   * Calls the backend API to initialize Digilocker and opens a pop-up.
   */
  const handleStartDigilockerFlow = async () => {
    setInitializingDigilocker(true);
    setDigilockerPopupOpen(false); // Reset in case it was true
    setPollingAadhaarData(false); // Reset polling state

    // Clear any existing polling interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }

    try {
      // 1. Call your backend API to initialize Digilocker
      const response = await axios.post("/api/initialize-digilocker");

      if (response.data.success) {
        const { token, clientId } = response.data;
        setClientIdForDigilocker(clientId); // Store clientId for later use

        // 2. Construct the Surepass Digilocker URL
        // IMPORTANT: Replace this with the actual Surepass Digilocker initiation URL
        // This URL typically takes the token and client_id, and a redirect_uri.
        // For demonstration, we'll use a placeholder URL.
        const digilockerUrl = `https://sandbox.surepass.app/digilocker/auth?token=${token}&client_id=${clientId}&redirect_uri=${encodeURIComponent(
          window.location.origin + "/digilocker-callback"
        )}`;

        // 3. Open a new pop-up window for the Digilocker flow
        popupRef.current = window.open(
          digilockerUrl,
          "digilockerPopup",
          "width=600,height=700,resizable=yes,scrollbars=yes"
        );

        if (popupRef.current) {
          setDigilockerPopupOpen(true);
          onShowToast?.(
            "Please complete the Aadhaar verification in the pop-up window.",
            "info"
          );

          // 4. Start polling for Aadhaar data after the pop-up opens
          // In a real scenario, the backend would receive a callback and then update status.
          // Here, we poll the download API until data is ready.
          setPollingAadhaarData(true);
          pollingIntervalRef.current = setInterval(() => {
            handlePollAadhaarData(clientId);
          }, 3000); // Poll every 3 seconds

          // Optional: Add a timeout to stop polling after a certain period (e.g., 5 minutes)
          setTimeout(() => {
            if (pollingIntervalRef.current && !verified) {
              clearInterval(pollingIntervalRef.current);
              pollingIntervalRef.current = null;
              setPollingAadhaarData(false);
              onShowToast?.(
                "Aadhaar verification timed out. Please try again or upload manually.",
                "error"
              );
              setShowManualAadhaarInput(true); // Offer manual upload on timeout
            }
          }, 5 * 60 * 1000); // 5 minutes timeout
        } else {
          throw new Error(
            "Failed to open Digilocker pop-up. Please allow pop-ups for this site."
          );
        }
      } else {
        throw new Error(
          response.data.error || "Failed to initialize Digilocker."
        );
      }
    } catch (error: any) {
      console.error("Digilocker initialization error:", error);
      onShowToast?.(
        error.message || "An error occurred during Digilocker initialization.",
        "error"
      );
      setInitializingDigilocker(false);
      setDigilockerPopupOpen(false);
      setPollingAadhaarData(false);
      setShowManualAadhaarInput(true); // Offer manual upload on error
    } finally {
      setInitializingDigilocker(false);
    }
  };

  /**
   * Polls the backend API to download Aadhaar data using the client ID.
   * @param clientId The client ID obtained from Digilocker initialization.
   */
  const handlePollAadhaarData = async (clientId: string) => {
    try {
      const response = await axios.post("/api/verify-aadhaar", { clientId }); // This maps to your download-aadhaar API

      if (response.data.success && response.data.data) {
        // Data is successfully retrieved
        const verifiedData = response.data.data as AadhaarData;
        setAadhaarData(verifiedData);

        // Update form state with verified data
        setValue(`persons.${index}.aadhaarVerified`, true);
        setValue(`persons.${index}.aadhaarData`, verifiedData);
        setValue(`persons.${index}.aadhaarNumber`, verifiedData.aadhaarNumber); // Populate Aadhaar number field

        onShowToast?.("Aadhaar verified successfully!", "success");

        // Stop polling and close pop-up if it's still open
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        if (popupRef.current) {
          popupRef.current.close();
        }
        setPollingAadhaarData(false);
        setDigilockerPopupOpen(false);
      } else if (
        response.data.message === "Data not ready yet" ||
        !response.data.data
      ) {
        // Data is not yet ready, continue polling
        console.log("Aadhaar data not ready, continuing to poll...");
      } else {
        // Handle other errors from the download API
        throw new Error(response.data.error || "Failed to fetch Aadhaar data.");
      }
    } catch (error: any) {
      console.error("Aadhaar data polling error:", error);
      // Stop polling on critical errors
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      setPollingAadhaarData(false);
      setDigilockerPopupOpen(false);
      onShowToast?.(
        error.message ||
          "Failed to retrieve Aadhaar data. Please try again or upload manually.",
        "error"
      );
      setShowManualAadhaarInput(true); // Offer manual upload on error
    }
  };

  /**
   * Returns a status badge based on verification status.
   */
  const getStatusBadge = () => {
    if (verified) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          ✅ Verified
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        ❌ Not Verified
      </span>
    );
  };

  return (
    <>
      <div className="border border-gray-200 rounded-lg shadow-sm">
        <div className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-t-lg">
          <div className="flex items-center space-x-3">
            <span className="font-medium text-gray-900">
              🆔 Aadhaar Verification
            </span>
            {getStatusBadge()}
          </div>
        </div>

        <div className="p-4 border-t border-gray-200">
          {!verified && !showManualAadhaarInput && (
            <div className="space-y-4">
              <button
                type="button"
                onClick={handleStartDigilockerFlow}
                disabled={
                  initializingDigilocker ||
                  pollingAadhaarData ||
                  digilockerPopupOpen
                }
                className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center justify-center font-medium transition-colors duration-200"
              >
                {initializingDigilocker ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Initializing Digilocker...
                  </>
                ) : pollingAadhaarData ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Waiting for Digilocker verification...
                  </>
                ) : (
                  "Verify with Digilocker"
                )}
              </button>
              {(initializingDigilocker || pollingAadhaarData) && (
                <p className="text-center text-sm text-gray-600 mt-2">
                  Please complete the process in the pop-up window.
                </p>
              )}
              <button
                type="button"
                onClick={() => setShowManualAadhaarInput(true)}
                className="text-blue-600 hover:text-blue-800 text-sm w-full text-center mt-2"
              >
                Upload Aadhaar manually instead
              </button>
            </div>
          )}

          {/* Display verified Aadhaar data */}
          {verified && aadhaarData && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-900 mb-3">
                Verified Aadhaar Details:
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <strong>Name:</strong> {aadhaarData.name}
                </div>
                <div>
                  <strong>Father's Name:</strong> {aadhaarData.fatherName}
                </div>
                <div>
                  <strong>Gender:</strong> {aadhaarData.gender}
                </div>
                <div>
                  <strong>Aadhaar Number:</strong> {aadhaarData.aadhaarNumber}
                </div>
                <div className="md:col-span-2">
                  <strong>Address:</strong> {aadhaarData.address}
                </div>
              </div>
            </div>
          )}

          {/* Manual Aadhaar input fallback */}
          {showManualAadhaarInput && !verified && (
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 text-sm mb-3">
                  Enter your Aadhaar number below. You can upload the Aadhaar
                  card document later during the document submission process.
                </p>

                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Enter 12-digit Aadhaar number"
                    maxLength={12}
                    value={manualAadhaarNumber}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, ""); // Only allow digits
                      setManualAadhaarNumber(value);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  {/* Display validation error for manual input if any */}
                  {manualAadhaarNumber.length > 0 &&
                    manualAadhaarNumber.length !== 12 && (
                      <p className="text-red-500 text-sm mt-1">
                        Aadhaar must be a 12-digit number.
                      </p>
                    )}

                  <button
                    type="button"
                    onClick={() => {
                      if (manualAadhaarNumber.length === 12) {
                        // Save to form data
                        setValue(
                          `persons.${index}.aadhaarNumber`,
                          manualAadhaarNumber
                        );
                        setValue(`persons.${index}.aadhaarVerified`, true);
                        setShowManualAadhaarInput(false);
                        setManualAadhaarNumber("");
                        onShowToast?.(
                          "Aadhaar number saved for manual submission.",
                          "success"
                        );
                      } else {
                        onShowToast?.(
                          "Please enter a valid 12-digit Aadhaar number",
                          "error"
                        );
                      }
                    }}
                    disabled={manualAadhaarNumber.length !== 12}
                    className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                  >
                    Save Aadhaar Number
                  </button>
                </div>

                {/* Back to verification button */}
                <div className="mt-3">
                  <button
                    type="button"
                    onClick={() => setShowManualAadhaarInput(false)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    ← Back to verification options
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
