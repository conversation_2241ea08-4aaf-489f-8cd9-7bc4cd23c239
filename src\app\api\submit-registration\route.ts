import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    // Get current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { registrationId } = await request.json();

    // Validation
    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    // Find the registration record
    const existingRegistration = await prisma.basicCompanyDetailStep1.findFirst({
      where: {
        id: registrationId,
        userId: user.id
      }
    });

    if (!existingRegistration) {
      return NextResponse.json(
        { error: 'Registration not found' },
        { status: 404 }
      );
    }

    // Check if already submitted
    if (existingRegistration.status === 'SUBMITTED') {
      return NextResponse.json(
        { error: 'Registration has already been submitted' },
        { status: 400 }
      );
    }

    // Validate that all invited persons have submitted their forms AND documents
    const invitations = await prisma.personInvitation.findMany({
      where: {
        registrationId
      }
    });

    // Check if any invitation is still pending (hasn't submitted form yet)
    const pendingFormSubmissions = invitations.filter(inv => inv.status === 'PENDING');

    if (pendingFormSubmissions.length > 0) {
      const pendingDetails = pendingFormSubmissions.map(inv =>
        `${inv.personRole} (Status: ${inv.status})`
      ).join(', ');

      return NextResponse.json(
        {
          error: `Cannot submit registration yet. The following invited persons have not submitted their forms: ${pendingDetails}. Please wait for all invited persons to complete their form submission before submitting the registration.`
        },
        { status: 400 }
      );
    }

    // Check if any invitation hasn't completed document submission
    const pendingDocumentSubmissions = invitations.filter(inv => inv.status !== 'DOCUMENTS_SUBMITTED');

    if (pendingDocumentSubmissions.length > 0) {
      const pendingDetails = pendingDocumentSubmissions.map(inv =>
        `${inv.personRole} (Status: ${inv.status})`
      ).join(', ');

      return NextResponse.json(
        {
          error: `Cannot submit registration yet. The following invited persons have not completed their document upload: ${pendingDetails}. Please wait for all invited persons to complete their document submission before submitting the registration.`
        },
        { status: 400 }
      );
    }

    // Update status to SUBMITTED
    const updatedRegistration = await prisma.basicCompanyDetailStep1.update({
      where: {
        id: registrationId
      },
      data: {
        status: 'SUBMITTED',
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Registration submitted successfully',
      data: {
        id: updatedRegistration.id,
        status: updatedRegistration.status,
        submittedAt: updatedRegistration.updatedAt
      }
    });

  } catch (error) {
    console.error('Error submitting registration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
