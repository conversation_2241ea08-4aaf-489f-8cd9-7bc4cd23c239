"use client";

import { useState, useEffect } from "react";
import {
  Control,
  FieldErrors,
  UseFormSetValue,
  useWatch,
} from "react-hook-form";
import axios from "axios";

// Step 1: Use the complete and consistent type definition to avoid errors with parent components
interface PersonFormData {
  persons: Array<{
    firstName: string;
    middleName?: string;
    lastName?: string;
    email: string;
    contact: string;
    aadhaarNumber?: string;
    aadhaarVerified: boolean;
    aadhaarData?: unknown;
    panNumber?: string;
    panVerified: boolean;
    panData?: unknown;
    hasDIN?: boolean;
    dinNumber?: string;
    dinVerified: boolean;
    dinData?: unknown;
    addressVerificationMethod?: string;
    electricityConnectionNumber?: string;
    electricityOperatorCode?: string; // Correct field name
    electricityVerified?: boolean;
    electricityData?: unknown;
  }>;
}

interface AddressVerificationProps {
  index: number;
  control: Control<PersonFormData>;
  errors: FieldErrors<PersonFormData>;
  setValue: UseFormSetValue<PersonFormData>;
  onShowToast?: (message: string, type: "success" | "error" | "info") => void;
}

// Define the structure for an operator from our API
interface Operator {
  state: string;
  operator_code: string;
}

export default function AddressVerification({
  index,
  control,
  errors,
  setValue,
  onShowToast,
}: AddressVerificationProps) {
  const [verifying, setVerifying] = useState(false);
  const [electricityData, setElectricityData] = useState<any | null>(null);

  // Step 2: Add state to store the list of operators from the API
  const [operatorList, setOperatorList] = useState<Operator[]>([]);

  // Watch form values
  const addressMethod = useWatch({
    control,
    name: `persons.${index}.addressVerificationMethod`,
  });
  const connectionNumber = useWatch({
    control,
    name: `persons.${index}.electricityConnectionNumber`,
  });
  const operatorCode = useWatch({
    control,
    name: `persons.${index}.electricityOperatorCode`,
  });
  const electricityVerified = useWatch({
    control,
    name: `persons.${index}.electricityVerified`,
  });

  // Step 3: Fetch the operator list when the component first loads
  useEffect(() => {
    const fetchOperators = async () => {
      try {
        const response = await axios.get("/api/electricity");
        if (response.data.success) {
          setOperatorList(response.data.data);
        }
      } catch (error) {
        console.error("Failed to fetch electricity operators", error);
        onShowToast?.("Could not load electricity boards list.", "error");
      }
    };
    fetchOperators();
  }, [onShowToast]); // Dependency array ensures this runs only once

  const handleVerifyElectricity = async () => {
    if (!connectionNumber || !operatorCode) {
      onShowToast?.(
        "Please select a board and enter a connection number",
        "error"
      );
      return;
    }

    try {
      setVerifying(true);

      // Step 4: Send the correct operatorCode to the backend
      const response = await axios.post("/api/electricity", {
        connectionNumber,
        operatorCode,
      });

      if (response.data.success) {
        const verifiedData = response.data.data;
        // Find the full board name from our list to display it nicely
        const board = operatorList.find(
          (op) => op.operator_code === operatorCode
        );
        const displayData = {
          ...verifiedData,
          boardName: board?.state || operatorCode, // Use the full name for display
        };

        setElectricityData(displayData);
        setValue(`persons.${index}.electricityVerified`, true);
        setValue(`persons.${index}.electricityData`, displayData);
        onShowToast?.(" Electricity bill verified successfully!", "success");
      } else {
        throw new Error(
          response.data.error || "Electricity bill verification failed"
        );
      }
    } catch (error: any) {
      console.error("Electricity verification error:", error);
      onShowToast?.(
        error.response?.data?.error ||
          "Failed to verify. Please check details and try again.",
        "error"
      );
    } finally {
      setVerifying(false);
    }
  };

  const renderVerificationStatus = () => {
    if (electricityVerified) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          ✅ Verified
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        ❌ Not Verified
      </span>
    );
  };

  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="p-4 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="font-medium text-gray-900">
              🏠 Address Verification
            </span>
            {addressMethod === "electricity" && renderVerificationStatus()}
          </div>
        </div>
      </div>

      <div className="p-4 border-t border-gray-200">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Choose Address Verification Method
            </label>
            <select
              {...control.register(
                `persons.${index}.addressVerificationMethod`
              )}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg"
            >
              <option value="">Select verification method</option>
              <option value="electricity">Electricity Bill</option>
              <option value="gas">Gas Bill</option>
              <option value="wifi">WiFi Bill</option>
            </select>
          </div>

          {addressMethod === "electricity" && (
            <div className="space-y-4">
              {!electricityVerified ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Connection Number
                      </label>
                      <input
                        type="text"
                        {...control.register(
                          `persons.${index}.electricityConnectionNumber`,
                          {
                            required: "Connection number is required",
                          }
                        )}
                        placeholder="Enter connection number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                      />
                      {errors.persons?.[index]?.electricityConnectionNumber && (
                        <p className="text-red-600 text-sm mt-1">
                          {
                            errors.persons[index]?.electricityConnectionNumber
                              ?.message
                          }
                        </p>
                      )}
                    </div>

                    <div>
                      {/* Step 5: The text input is now a dropdown menu */}
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Electricity Board
                      </label>
                      <select
                        {...control.register(
                          `persons.${index}.electricityOperatorCode`,
                          {
                            required: "Please select a board",
                          }
                        )}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                      >
                        <option value="">-- Select a board --</option>
                        {operatorList.map((op) => (
                          <option
                            key={op.operator_code}
                            value={op.operator_code}
                          >
                            {op.state}
                          </option>
                        ))}
                      </select>
                      {errors.persons?.[index]?.electricityOperatorCode && (
                        <p className="text-red-600 text-sm mt-1">
                          {
                            errors.persons[index]?.electricityOperatorCode
                              ?.message
                          }
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={handleVerifyElectricity}
                      disabled={verifying || !connectionNumber || !operatorCode}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
                    >
                      {verifying ? "Verifying..." : "Verify"}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-900 mb-3">
                    Verified Electricity Bill Details:
                  </h4>
                  {electricityData && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                      <div>
                        <strong>Consumer Name:</strong>{" "}
                        {electricityData.full_name}
                      </div>
                      <div>
                        <strong>Connection Number:</strong>{" "}
                        {electricityData.customer_id}
                      </div>
                      <div>
                        <strong>Board Name:</strong> {electricityData.boardName}
                      </div>
                      <div>
                        <strong>Bill Amount:</strong>{" "}
                        {electricityData.bill_amount ?? "N/A"}
                      </div>
                      <div>
                        <strong>Bill Date:</strong>{" "}
                        {electricityData.bill_date ?? "N/A"}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
