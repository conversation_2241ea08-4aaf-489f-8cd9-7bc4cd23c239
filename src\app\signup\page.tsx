import AuthForm from '@/components/AuthForm';
import Navbar from '@/components/Navbar';
import Footer from '@/components/footer';

export default function SignupPage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />
      <div className="flex-1 flex">
        {/* Left side - Lottie Animation */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-8">
          <div className="w-full h-full max-w-lg">
            <iframe
              src="https://lottie.host/embed/7c4c6114-a831-4cd6-8059-a48d024e90bc/MJ1c9olCyA.lottie"
              className="w-full h-full min-h-[400px] border-0 rounded-lg"
              title="Signup Animation"
            />
          </div>
        </div>

        {/* Right side - Signup Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <AuthForm type="signup" />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
