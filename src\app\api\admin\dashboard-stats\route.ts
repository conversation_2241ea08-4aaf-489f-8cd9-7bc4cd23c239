import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get total users count
    const totalUsers = await prisma.user.count();

    // Get applications counts by status
    const [
      totalApplications,
      inProgressApplications,
      documentNeededApplications,
      completedApplications
    ] = await Promise.all([
      prisma.basicCompanyDetailStep1.count(),
      prisma.basicCompanyDetailStep1.count({
        where: { status: 'IN_PROGRESS' }
      }),
      prisma.basicCompanyDetailStep1.count({
        where: { 
          OR: [
            { status: 'SUBMITTED' },
            { status: 'DOCUMENTS_PENDING' }
          ]
        }
      }),
      prisma.basicCompanyDetailStep1.count({
        where: { status: 'COMPLETED' }
      })
    ]);

    return NextResponse.json({
      totalUsers,
      totalApplications,
      inProgressApplications,
      documentNeededApplications,
      completedApplications
    });

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
