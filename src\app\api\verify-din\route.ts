import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { dinNumber } = await request.json();

    // Validation
    if (!dinNumber || typeof dinNumber !== "string" || dinNumber.length !== 8) {
      return NextResponse.json(
        { success: false, error: "Valid 8-digit DIN number is required" },
        { status: 400 }
      );
    }

    const surepassApiUrl = "https://sandbox.surepass.io/api/v1/corporate/din";
    const surepassApiKey = process.env.SUREPASS_API_TOKEN;

    if (!surepassApiKey) {
      console.error("SUREPASS_API_TOKEN environment variable is not set.");
      return NextResponse.json(
        {
          success: false,
          error: "Server configuration error: SurePass API token missing.",
        },
        { status: 500 }
      );
    }

    // Log request details for debugging
    console.log("Making request to SurePass DIN API:");
    console.log("URL:", surepassApiUrl);
    console.log("DIN Number:", dinNumber);
    console.log(
      "API Token (first 50 chars):",
      surepassApiKey.substring(0, 50) + "..."
    );

    const requestBody = {
      id_number: dinNumber,
    };

    console.log("Request Body:", JSON.stringify(requestBody));

    const surepassResponse = await fetch(surepassApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${surepassApiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    // Log the response for debugging
    console.log("SurePass DIN API Response Status:", surepassResponse.status);
    console.log(
      "SurePass DIN API Response Headers:",
      Object.fromEntries(surepassResponse.headers.entries())
    );

    // Get response text first to check if it's HTML or JSON
    const responseText = await surepassResponse.text();
    console.log(
      "SurePass DIN API Response Text (first 500 chars):",
      responseText.substring(0, 500)
    );

    if (!surepassResponse.ok) {
      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Failed to parse error response as JSON:", parseError);
        return NextResponse.json(
          {
            success: false,
            error: `SurePass DIN API returned non-JSON response. Status: ${surepassResponse.status}`,
            details: { responseText: responseText.substring(0, 1000) },
          },
          { status: surepassResponse.status }
        );
      }

      console.error("SurePass DIN API error:", errorData);
      return NextResponse.json(
        {
          success: false,
          error: errorData.message || "SurePass DIN verification failed.",
          details: errorData,
        },
        { status: surepassResponse.status }
      );
    }

    // Try to parse the successful response
    let surepassData;
    try {
      surepassData = JSON.parse(responseText);
    } catch (parseError) {
      console.error("Failed to parse successful response as JSON:", parseError);
      return NextResponse.json(
        {
          success: false,
          error: "SurePass DIN API returned invalid JSON response",
          details: { responseText: responseText.substring(0, 1000) },
        },
        { status: 500 }
      );
    }

    // Check if SurePass returned data (successful response format)
    if (
      surepassData.success &&
      surepassData.data &&
      surepassData.data.din_number
    ) {
      // Format the data to match frontend expectations
      const verifiedData = {
        directorName: surepassData.data.full_name || "N/A",
        dateOfBirth: surepassData.data.dob || "N/A",
        dinNumber: surepassData.data.din_number,
        status: surepassData.data.status === "success" ? "Active" : "Inactive",
        associatedCompanies: surepassData.data.companies_associated
          ? `${surepassData.data.companies_associated.length} Companies`
          : "0 Companies",
      };

      console.log("DIN verification successful:", verifiedData);

      return NextResponse.json({
        success: true,
        message: "DIN verified successfully",
        data: verifiedData,
      });
    } else {
      // If SurePass API call was OK, but no data was returned
      console.log("SurePass DIN returned no data:", surepassData);
      return NextResponse.json(
        {
          success: false,
          error: "DIN verification failed - no data returned from SurePass.",
          details: surepassData,
        },
        { status: 200 }
      );
    }
  } catch (error) {
    console.error("Internal API error during DIN verification:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error during DIN verification.",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
