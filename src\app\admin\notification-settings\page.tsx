'use client';

import { useState } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/footer';

export default function NotificationSettingsPage() {
  const [emailSettings, setEmailSettings] = useState({
    enabled: false,
    service: 'nodemailer', // nodemailer, sendgrid, ses, resend
    smtpHost: '',
    smtpPort: '587',
    smtpUser: '',
    smtpPass: '',
    fromEmail: '',
    fromName: 'TaxLegit'
  });

  const [whatsappSettings, setWhatsappSettings] = useState({
    enabled: false,
    service: 'twilio', // twilio, 360dialog, gupshup
    accountSid: '',
    authToken: '',
    fromNumber: ''
  });

  const handleSaveSettings = () => {
    // Save settings to environment variables or database
    console.log('Email Settings:', emailSettings);
    console.log('WhatsApp Settings:', whatsappSettings);
    alert('Settings saved! Please restart the application for changes to take effect.');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />
      
      <div className="flex-1 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">
              Notification Settings
            </h1>

            {/* Email Settings */}
            <div className="mb-12">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">📧 Email Settings</h2>
              
              <div className="space-y-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="email-enabled"
                    checked={emailSettings.enabled}
                    onChange={(e) => setEmailSettings(prev => ({ ...prev, enabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="email-enabled" className="ml-2 text-sm font-medium text-gray-900">
                    Enable Email Notifications
                  </label>
                </div>

                {emailSettings.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-gray-50 rounded-lg">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email Service
                      </label>
                      <select
                        value={emailSettings.service}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, service: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="nodemailer">Nodemailer (SMTP)</option>
                        <option value="sendgrid">SendGrid</option>
                        <option value="ses">AWS SES</option>
                        <option value="resend">Resend</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SMTP Host
                      </label>
                      <input
                        type="text"
                        value={emailSettings.smtpHost}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpHost: e.target.value }))}
                        placeholder="smtp.gmail.com"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SMTP Port
                      </label>
                      <input
                        type="text"
                        value={emailSettings.smtpPort}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPort: e.target.value }))}
                        placeholder="587"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SMTP Username
                      </label>
                      <input
                        type="text"
                        value={emailSettings.smtpUser}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpUser: e.target.value }))}
                        placeholder="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        SMTP Password
                      </label>
                      <input
                        type="password"
                        value={emailSettings.smtpPass}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, smtpPass: e.target.value }))}
                        placeholder="your-app-password"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        From Email
                      </label>
                      <input
                        type="email"
                        value={emailSettings.fromEmail}
                        onChange={(e) => setEmailSettings(prev => ({ ...prev, fromEmail: e.target.value }))}
                        placeholder="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* WhatsApp Settings */}
            <div className="mb-12">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">📱 WhatsApp Settings</h2>
              
              <div className="space-y-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="whatsapp-enabled"
                    checked={whatsappSettings.enabled}
                    onChange={(e) => setWhatsappSettings(prev => ({ ...prev, enabled: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="whatsapp-enabled" className="ml-2 text-sm font-medium text-gray-900">
                    Enable WhatsApp Notifications
                  </label>
                </div>

                {whatsappSettings.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-gray-50 rounded-lg">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        WhatsApp Service
                      </label>
                      <select
                        value={whatsappSettings.service}
                        onChange={(e) => setWhatsappSettings(prev => ({ ...prev, service: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="twilio">Twilio WhatsApp API</option>
                        <option value="360dialog">360Dialog</option>
                        <option value="gupshup">Gupshup</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account SID
                      </label>
                      <input
                        type="text"
                        value={whatsappSettings.accountSid}
                        onChange={(e) => setWhatsappSettings(prev => ({ ...prev, accountSid: e.target.value }))}
                        placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Auth Token
                      </label>
                      <input
                        type="password"
                        value={whatsappSettings.authToken}
                        onChange={(e) => setWhatsappSettings(prev => ({ ...prev, authToken: e.target.value }))}
                        placeholder="your-auth-token"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        From Number
                      </label>
                      <input
                        type="text"
                        value={whatsappSettings.fromNumber}
                        onChange={(e) => setWhatsappSettings(prev => ({ ...prev, fromNumber: e.target.value }))}
                        placeholder="+14155238886"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSaveSettings}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium shadow-lg hover:shadow-xl transition-all"
              >
                Save Settings
              </button>
            </div>

            {/* Instructions */}
            <div className="mt-12 p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">📋 Setup Instructions</h3>
              <div className="space-y-2 text-sm text-blue-800">
                <p><strong>For Email (Gmail):</strong> Use your Gmail address and generate an App Password in your Google Account settings.</p>
                <p><strong>For WhatsApp (Twilio):</strong> Sign up at Twilio, get your Account SID and Auth Token, and enable WhatsApp API.</p>
                <p><strong>Environment Variables:</strong> Add these settings to your .env file for production use.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
