"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import axios from "axios";

interface DocumentUploadProps {
  personRole: string;
  invitationId?: string;
  registrationId: string;
  personIndex: number;
  addressVerificationMethod?: string;
  onSubmitSuccess: () => void;
  onShowToast?: (message: string, type: "success" | "error" | "info") => void;
}

interface DocumentFormData {
  aadhaarDoc: FileList;
  panDoc: FileList;
  photo: FileList;
  signature: FileList;
  geoTagImage: FileList;
  bankTransactionDetails: FileList;
  addressVerificationDoc: FileList;
}

export default function DocumentUpload({
  personRole,
  invitationId,
  registrationId,
  personIndex,
  addressVerificationMethod,
  onSubmitSuccess,
  onShowToast,
}: DocumentUploadProps) {
  console.log("DocumentUpload component rendered with props:", {
    personRole,
    invitationId,
    registrationId,
    personIndex,
  });
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {}
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<DocumentFormData>();

  const uploadFile = async (file: File, type: string): Promise<string> => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);
    formData.append("registrationId", registrationId);
    formData.append("personIndex", personIndex.toString());

    const response = await axios.post("/api/upload-document", formData, {
      headers: { "Content-Type": "multipart/form-data" },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress((prev) => ({ ...prev, [type]: progress }));
        }
      },
    });

    return response.data.url;
  };

  const onSubmit = async (data: DocumentFormData) => {
    try {
      setLoading(true);

      // For Person A (no invitationId), check if all invited persons have completed documents
      if (!invitationId) {
        console.log(
          "Checking if all invited persons have completed documents..."
        );

        try {
          const statusResponse = await axios.get(
            `/api/get-person-status?registrationId=${registrationId}`
          );

          if (statusResponse.data.success) {
            const personStatuses = statusResponse.data.data.personStatuses;
            const pendingInvitations: Array<{ index: number; status: string }> =
              [];

            // Check all persons except Person A (index 0)
            Object.keys(personStatuses).forEach((index) => {
              const personIndex = parseInt(index);
              const status = personStatuses[personIndex];

              if (
                personIndex !== 0 &&
                status.hasInvitation &&
                status.inviteStatus !== "documents_submitted"
              ) {
                pendingInvitations.push({
                  index: personIndex,
                  status: status.inviteStatus,
                });
              }
            });

            if (pendingInvitations.length > 0) {
              const pendingList = pendingInvitations
                .map((p) => `Person ${p.index + 1} (Status: ${p.status})`)
                .join(", ");

              onShowToast?.(
                `❌ Cannot submit documents yet!\n\nThe following invited persons have not completed their document submission:\n${pendingList}\n\nPlease wait for all invited persons to complete their documents before submitting yours.`,
                "error"
              );
              return;
            }
          }
        } catch (error) {
          console.error("Error checking person status:", error);
          onShowToast?.(
            "❌ Error checking invitation status. Please try again.",
            "error"
          );
          return;
        }
      }

      // Upload all documents
      const uploads = await Promise.all([
        uploadFile(data.aadhaarDoc[0], "aadhaar"),
        uploadFile(data.panDoc[0], "pan"),
        uploadFile(data.photo[0], "photo"),
        uploadFile(data.signature[0], "signature"),
        uploadFile(data.geoTagImage[0], "geotag"),
        uploadFile(data.bankTransactionDetails[0], "banktransaction"),
        uploadFile(data.addressVerificationDoc[0], "addressverification"),
      ]);

      // Save document URLs to database
      const documentData = {
        invitationId,
        registrationId,
        personIndex,
        aadhaarDocUrl: uploads[0],
        panDocUrl: uploads[1],
        photoUrl: uploads[2],
        signatureUrl: uploads[3],
        geoTagImageUrl: uploads[4],
        bankTransactionDetailsUrl: uploads[5],
        addressVerificationDocUrl: uploads[6],
        aadhaarDocUploaded: true,
        panDocUploaded: true,
        photoUploaded: true,
        signatureUploaded: true,
        geoTagImageUploaded: true,
        bankTransactionDetailsUploaded: true,
        addressVerificationDocUploaded: true,
        documentsCompleted: true,
      };

      await axios.post("/api/save-person-documents", documentData);

      onShowToast?.("Documents uploaded successfully!", "success");
      onSubmitSuccess();
    } catch (error: any) {
      console.error("Error uploading documents:", error);
      const errorMessage =
        error.response?.data?.error ||
        "Error uploading documents. Please try again.";
      onShowToast?.(`❌ ${errorMessage}`, "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-2xl shadow-lg p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Document Upload
          </h1>
          <p className="text-gray-600">
            Upload required documents for {personRole}
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          {/* Aadhaar Document */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              📄 Aadhaar Card
            </h3>
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              {...register("aadhaarDoc", {
                required: "Aadhaar document is required",
              })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.aadhaarDoc && (
              <p className="text-red-600 text-sm mt-2">
                {errors.aadhaarDoc.message}
              </p>
            )}
            {uploadProgress.aadhaar && (
              <div className="mt-2 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress.aadhaar}%` }}
                ></div>
              </div>
            )}
          </div>

          {/* PAN Document */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              🆔 PAN Card
            </h3>
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              {...register("panDoc", { required: "PAN document is required" })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.panDoc && (
              <p className="text-red-600 text-sm mt-2">
                {errors.panDoc.message}
              </p>
            )}
          </div>

          {/* Photo */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              📸 Passport Size Photo
            </h3>
            <input
              type="file"
              accept=".jpg,.jpeg,.png"
              {...register("photo", { required: "Photo is required" })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.photo && (
              <p className="text-red-600 text-sm mt-2">
                {errors.photo.message}
              </p>
            )}
          </div>

          {/* Signature */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              ✍️ Signature
            </h3>
            <input
              type="file"
              accept=".jpg,.jpeg,.png"
              {...register("signature", { required: "Signature is required" })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.signature && (
              <p className="text-red-600 text-sm mt-2">
                {errors.signature.message}
              </p>
            )}
          </div>

          {/* Geo Tag Image */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              📍 Geo Tag Image
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              Please upload a photo taken at your current location with GPS
              coordinates enabled.
            </p>
            <input
              type="file"
              accept=".jpg,.jpeg,.png"
              {...register("geoTagImage", {
                required: "Geo tag image is required",
              })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.geoTagImage && (
              <p className="text-red-600 text-sm mt-2">
                {errors.geoTagImage.message}
              </p>
            )}
          </div>

          {/* Bank Transaction Details */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              🏦 Last 2 Month Bank Transaction Details
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              Please upload your bank statement or transaction details for the
              last 2 months.
            </p>
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              {...register("bankTransactionDetails", {
                required: "Bank transaction details are required",
              })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.bankTransactionDetails && (
              <p className="text-red-600 text-sm mt-2">
                {errors.bankTransactionDetails.message}
              </p>
            )}
          </div>

          {/* Address Verification Document */}
          <div className="border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              🏠 Address Verification Document
              {addressVerificationMethod && (
                <span className="text-sm font-normal text-blue-600 ml-2">
                  (
                  {addressVerificationMethod === "electricity"
                    ? "Electricity Bill"
                    : addressVerificationMethod === "gas"
                    ? "Gas Bill"
                    : addressVerificationMethod === "wifi"
                    ? "WiFi Bill"
                    : "Address Proof"}
                  )
                </span>
              )}
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              {addressVerificationMethod === "electricity"
                ? "Please upload your electricity bill document."
                : addressVerificationMethod === "gas"
                ? "Please upload your gas bill document."
                : addressVerificationMethod === "wifi"
                ? "Please upload your WiFi/internet bill document."
                : "Please upload your address verification document."}
            </p>
            <input
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              {...register("addressVerificationDoc", {
                required: "Address verification document is required",
              })}
              className="w-full p-3 border border-gray-300 rounded-lg"
            />
            {errors.addressVerificationDoc && (
              <p className="text-red-600 text-sm mt-2">
                {errors.addressVerificationDoc.message}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <div className="text-center">
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center space-x-2 mx-auto"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Uploading Documents...</span>
                </>
              ) : (
                <>
                  <span>📤</span>
                  <span>Submit Documents</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
