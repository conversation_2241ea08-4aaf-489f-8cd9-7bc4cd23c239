import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth";
import J<PERSON><PERSON><PERSON> from "jszip";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

const s3Client = new S3Client({
  region: process.env.AWS_REGION || "ap-south-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

// Helper function to extract S3 key from URL
function extractS3Key(fileUrl: string): string {
  if (fileUrl.includes(".s3.") || fileUrl.includes(".s3-")) {
    const urlParts = fileUrl.split("/");
    const bucketIndex = urlParts.findIndex(
      (part) => part.includes(".s3.") || part.includes(".s3-")
    );
    if (bucketIndex !== -1 && bucketIndex < urlParts.length - 1) {
      return urlParts.slice(bucketIndex + 1).join("/");
    }
  }
  return fileUrl; // Return as-is if it's already a key
}

// Helper function to generate presigned URL
async function generatePresignedUrl(s3Key: string): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: process.env.AWS_S3_BUCKET_NAME || "taxlegit-documents",
    Key: s3Key,
  });
  return await getSignedUrl(s3Client, command, { expiresIn: 300 }); // 5 minutes
}

export async function POST(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    const { applicationId, companyName, documentUrls } = await request.json();

    if (!documentUrls || documentUrls.length === 0) {
      return NextResponse.json(
        { error: "No documents to download" },
        { status: 400 }
      );
    }

    const zip = new JSZip();

    // Group documents by person
    const documentsByPerson: { [key: string]: any[] } = {};

    documentUrls.forEach((doc: any) => {
      if (!documentsByPerson[doc.personName]) {
        documentsByPerson[doc.personName] = [];
      }
      documentsByPerson[doc.personName].push(doc);
    });

    // Download documents and organize by person folders
    for (const [personName, docs] of Object.entries(documentsByPerson)) {
      const personFolder = zip.folder(personName);

      for (const doc of docs) {
        try {
          console.log(
            `Processing: ${doc.name} for ${personName} from ${doc.url}`
          );

          // Extract S3 key and generate presigned URL
          const s3Key = extractS3Key(doc.url);
          const presignedUrl = await generatePresignedUrl(s3Key);

          console.log(
            `Downloading: ${doc.name} for ${personName} using presigned URL`
          );
          const response = await fetch(presignedUrl);
          if (response.ok) {
            const buffer = await response.arrayBuffer();
            const extension = getFileExtension(doc.url);
            const fileName = `${doc.name.replace(
              /[^a-zA-Z0-9]/g,
              "_"
            )}.${extension}`;
            personFolder?.file(fileName, buffer);
            console.log(`Added to zip: ${personName}/${fileName}`);
          } else {
            console.error(
              `Failed to download ${doc.name} for ${personName}: ${response.status} ${response.statusText}`
            );
          }
        } catch (error) {
          console.error(
            `Error downloading ${doc.name} for ${personName}:`,
            error
          );
          // Continue with other files even if one fails
        }
      }
    }

    // Generate zip file
    const zipBuffer = await zip.generateAsync({ type: "arraybuffer" });

    return new NextResponse(zipBuffer, {
      headers: {
        "Content-Type": "application/zip",
        "Content-Disposition": `attachment; filename="${companyName}_all_documents.zip"`,
      },
    });
  } catch (error) {
    console.error("Error creating all documents zip:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function getFileExtension(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const extension = pathname.split(".").pop()?.toLowerCase();

    // Common file extensions
    const validExtensions = [
      "pdf",
      "jpg",
      "jpeg",
      "png",
      "gif",
      "doc",
      "docx",
      "txt",
    ];

    if (extension && validExtensions.includes(extension)) {
      return extension;
    }

    // Default based on common document types
    return "pdf";
  } catch {
    return "pdf";
  }
}
