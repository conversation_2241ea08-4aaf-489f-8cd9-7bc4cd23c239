import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getCurrentUser } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const {
      registrationId,
      isDirectorAddressSame,
      addressProofType,
      addressProofUrl,
      totalShareCapital,
      shareRatios
    } = await request.json();

    // Validation
    if (!registrationId) {
      return NextResponse.json(
        { error: 'Registration ID is required' },
        { status: 400 }
      );
    }

    if (typeof isDirectorAddressSame !== 'boolean') {
      return NextResponse.json(
        { error: 'Director address preference is required' },
        { status: 400 }
      );
    }

    if (!totalShareCapital || totalShareCapital <= 0) {
      return NextResponse.json(
        { error: 'Valid share capital amount is required' },
        { status: 400 }
      );
    }

    if (!shareRatios || !Array.isArray(shareRatios) || shareRatios.length === 0) {
      return NextResponse.json(
        { error: 'Share ratios are required' },
        { status: 400 }
      );
    }

    // Validate share ratios total to 100%
    const totalPercentage = shareRatios.reduce((sum, ratio) => {
      const percentage = Number(ratio.sharePercentage) || 0;
      return sum + percentage;
    }, 0);

    if (Math.abs(totalPercentage - 100) > 0.1) { // Allow small floating point differences
      return NextResponse.json(
        { error: `Share ratios must total exactly 100%. Current total: ${totalPercentage}%` },
        { status: 400 }
      );
    }

    // Validate address proof if director address is different
    if (!isDirectorAddressSame && !addressProofType) {
      return NextResponse.json(
        { error: 'Address proof type is required when director address is different' },
        { status: 400 }
      );
    }

    // Check if company registration exists
    const existingRegistration = await prisma.basicCompanyDetailStep1.findFirst({
      where: {
        userId: user.id,
        id: registrationId // registrationId is actually the BasicCompanyDetailStep1 id
      }
    });

    if (!existingRegistration) {
      return NextResponse.json(
        { error: 'Company registration not found' },
        { status: 404 }
      );
    }

    // Update company registration with address and capital info
    const updatedRegistration = await prisma.basicCompanyDetailStep1.update({
      where: {
        id: existingRegistration.id
      },
      data: {
        // Address information
        isDirectorAddressSame: isDirectorAddressSame,
        addressProofType: addressProofType || null,
        addressProofUrl: addressProofUrl || null,

        // Share capital information
        totalShareCapital: Number(totalShareCapital), // Convert string to number
        shareRatios: shareRatios, // Store as JSON

        // Update timestamp
        updatedAt: new Date()
      }
    });

    // Update person details with share ratios
    for (const shareRatio of shareRatios) {
      await prisma.personDetail.updateMany({
        where: {
          userId: user.id,
          registrationId: registrationId,
          personIndex: shareRatio.personIndex
        },
        data: {
          sharePercentage: Number(shareRatio.sharePercentage) // Convert string to number
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Company address and share capital saved successfully',
      data: {
        registrationId: registrationId,
        isDirectorAddressSame: isDirectorAddressSame,
        addressProofType: addressProofType,
        totalShareCapital: totalShareCapital,
        shareRatios: shareRatios
      }
    });

  } catch (error) {
    console.error('Error saving company address & capital:', error);
    
    // More detailed error logging
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
