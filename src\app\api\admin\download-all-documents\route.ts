import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import J<PERSON><PERSON><PERSON> from 'jszip';

export async function POST(request: NextRequest) {
  try {
    // Get current user and check if admin
    const user = await getCurrentUser();
    if (!user || user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { applicationId, companyName, documentUrls } = await request.json();

    if (!documentUrls || documentUrls.length === 0) {
      return NextResponse.json(
        { error: 'No documents to download' },
        { status: 400 }
      );
    }

    const zip = new JSZip();

    // Group documents by person
    const documentsByPerson: { [key: string]: any[] } = {};
    
    documentUrls.forEach((doc: any) => {
      if (!documentsByPerson[doc.personName]) {
        documentsByPerson[doc.personName] = [];
      }
      documentsByPerson[doc.personName].push(doc);
    });

    // Download documents and organize by person folders
    for (const [personName, docs] of Object.entries(documentsByPerson)) {
      const personFolder = zip.folder(personName);
      
      for (const doc of docs) {
        try {
          console.log(`Downloading: ${doc.name} for ${personName} from ${doc.url}`);
          const response = await fetch(doc.url);
          if (response.ok) {
            const buffer = await response.arrayBuffer();
            const extension = getFileExtension(doc.url);
            const fileName = `${doc.name.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;
            personFolder?.file(fileName, buffer);
            console.log(`Added to zip: ${personName}/${fileName}`);
          } else {
            console.error(`Failed to download ${doc.name} for ${personName}: ${response.status} ${response.statusText}`);
          }
        } catch (error) {
          console.error(`Error downloading ${doc.name} for ${personName}:`, error);
          // Continue with other files even if one fails
        }
      }
    }

    // Generate zip file
    const zipBuffer = await zip.generateAsync({ type: 'arraybuffer' });

    return new NextResponse(zipBuffer, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename="${companyName}_all_documents.zip"`
      }
    });

  } catch (error) {
    console.error('Error creating all documents zip:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getFileExtension(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const extension = pathname.split('.').pop()?.toLowerCase();

    // Common file extensions
    const validExtensions = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'doc', 'docx', 'txt'];

    if (extension && validExtensions.includes(extension)) {
      return extension;
    }

    // Default based on common document types
    return 'pdf';
  } catch {
    return 'pdf';
  }
}
