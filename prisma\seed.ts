import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  await prisma.otp.deleteMany({});
  await prisma.user.deleteMany({});

  // Create a single admin user with hashed password
  const adminPassword = await bcrypt.hash('admin123', 10);

  const adminUser = {
    firstName: 'Super',
    lastName: 'Admin',
    email: '<EMAIL>',
    contact: '+1234567890',
    password: adminPassword,
    role: 'ADMIN' as const,
  };

  const regularUser = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    contact: '+1555123456',
    role: 'USER' as const,
  };

  // Insert admin user
  await prisma.user.create({ data: adminUser });
  console.log(`✅ Created admin: ${adminUser.email} | Password: admin123`);

  // Insert regular user
  await prisma.user.create({ data: regularUser });
  console.log(`✅ Created user: ${regularUser.email} | Uses OTP`);

  console.log('\n🚀 Seed data inserted successfully');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
