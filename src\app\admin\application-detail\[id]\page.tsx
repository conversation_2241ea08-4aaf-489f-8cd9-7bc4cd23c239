'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import axios from 'axios';
import CompanyDetailCard from '@/components/admin/detail/CompanyDetailCard';
import PersonDetailCard from '@/components/admin/detail/PersonDetailCard';
import ActionButtons from '@/components/admin/detail/ActionButtons';

interface CompanyDetail {
  id: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  state: string;
  companyName: string;
  alternateName?: string;
  businessKeywords: string;
  generatedObjective?: string;
  companyType?: string;
  isDirectorAddressSame?: boolean;
  addressProofType?: string;
  addressProofUrl?: string;
  totalShareCapital?: number;
  shareRatios?: any;
  status: string;
  createdAt: string;
  updatedAt: string;
  user: {
    firstName: string;
    middleName?: string;
    lastName?: string;
    email?: string;
    contact?: string;
  };
}

interface PersonDetail {
  id: string;
  invitationId?: string;
  personIndex: number;
  personRole: string;
  firstName: string;
  middleName?: string;
  lastName?: string;
  email: string;
  contact: string;
  aadhaarVerified: boolean;
  panVerified: boolean;
  hasDIN: boolean;
  dinVerified: boolean;
  electricityVerified: boolean;
  sharePercentage?: number;
  isCompleted: boolean;
  documentsCompleted: boolean;
  aadhaarDocUrl?: string;
  panDocUrl?: string;
  photoUrl?: string;
  signatureUrl?: string;
  geoTagImageUrl?: string;
  bankTransactionDetailsUrl?: string;
  addressVerificationDocUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export default function ApplicationDetailPage() {
  const params = useParams();
  const applicationId = params.id as string;
  
  const [companyDetail, setCompanyDetail] = useState<CompanyDetail | null>(null);
  const [personDetails, setPersonDetails] = useState<PersonDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchApplicationDetail = async () => {
      try {
        const response = await axios.get(`/api/admin/application-detail/${applicationId}`);
        if (response.data.success) {
          setCompanyDetail(response.data.companyDetail);
          setPersonDetails(response.data.personDetails);
        } else {
          setError('Failed to fetch application details');
        }
      } catch (error) {
        console.error('Error fetching application detail:', error);
        setError('Error loading application details');
      } finally {
        setLoading(false);
      }
    };

    if (applicationId) {
      fetchApplicationDetail();
    }
  }, [applicationId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !companyDetail) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
          <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
          <p className="text-red-700">{error || 'Application not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Application Details</h1>
              <p className="text-gray-600 mt-2">
                Company: {companyDetail.companyName} | Status: {companyDetail.status}
              </p>
            </div>
            <button
              onClick={() => window.close()}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <ActionButtons 
          applicationId={applicationId}
          companyDetail={companyDetail}
          personDetails={personDetails}
        />

        {/* Company Details */}
        <CompanyDetailCard companyDetail={companyDetail} />

        {/* Person Details */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-gray-900">Person Details</h2>
          {personDetails.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-6 text-center">
              <p className="text-gray-500">No person details found for this application.</p>
            </div>
          ) : (
            personDetails.map((person, index) => (
              <PersonDetailCard 
                key={person.id} 
                person={person} 
                index={index}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
}
