"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import axios from "axios";
import Navbar from "@/components/Navbar";
import Footer from "@/components/footer";
import CompanyTypeModal from "@/components/CompanyTypeModal";
import ProgressIndicator from "@/components/ProgressIndicator";
import ToastNotification from "@/components/ToastNotification";
import PersonForm from "@/components/registration/PersonForm";
import CompanyDetailsForm from "@/components/registration/CompanyDetailsForm";
import AddressCapitalForm from "@/components/registration/AddressCapitalForm";
import RegistrationStatus from "@/components/registration/RegistrationStatus";
import RegistrationReview from "@/components/registration/RegistrationReview";
import DocumentUpload from "@/components/registration/DocumentUpload";
import RegistrationDebug from "@/components/RegistrationDebug";
import {
  useRegistrationStore,
  useCompanyData,
  usePersonsData,
  useFormProgress,
} from "@/store/registrationStore";
import { useFormSync } from "@/hooks/useFormSync";

interface CompanyType {
  id: string;
  name: string;
  code: string;
  description: string;
  minCapital: string;
  minShareCapital: number;
  icon: string;
}
interface PersonData {
  firstName: string;
  middleName?: string;
  lastName?: string;
  email: string;
  contact: string;
  aadhaarNumber?: string;
  aadhaarVerified: boolean;
  aadhaarData?: unknown;
  panNumber?: string;
  panVerified: boolean;
  panData?: unknown;
  hasDIN?: boolean;
  dinNumber?: string;
  dinVerified: boolean;
  dinData?: unknown;

  electricityConnectionNumber?: string;
  electricityBoardName?: string;
  electricityVerified?: boolean;
  electricityData?: unknown;
  addressVerificationMethod?: string;
}

interface PersonDetailsFormData {
  persons: PersonData[];
}

export default function RegisterPage() {
  // Zustand stores
  const {
    selectedCompanyType: zustandCompanyType,
    registrationId: zustandRegistrationId,
    registrationStatus,
    currentStep,
    isSubmitting,
    setCompanyType,
    setRegistrationId: setZustandRegistrationId,
    setCurrentStep,
    setRegistrationStatus,
    setIsSubmitting,
    resetForm,
    updateLastSaved,
  } = useRegistrationStore();

  const { persons, setPersons } = usePersonsData();

  // Convert Zustand CompanyType to local CompanyType for components
  const selectedCompanyType: CompanyType | null = zustandCompanyType
    ? {
        id: zustandCompanyType.id,
        name: zustandCompanyType.name,
        code: zustandCompanyType.code,
        description: zustandCompanyType.description,
        minCapital: `₹${zustandCompanyType.minShareCapital.toLocaleString()}`,
        minShareCapital: zustandCompanyType.minShareCapital,
        icon: getCompanyIcon(zustandCompanyType.code),
      }
    : null;

  // Helper function to get company icon
  function getCompanyIcon(code: string): string {
    switch (code) {
      case "PVT":
        return "🏢";
      case "OPC":
        return "👤";
      case "LLP":
        return "🤝";
      case "SECTION8":
        return "🏛️";
      default:
        return "🏢";
    }
  }

  // Local state for UI
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [linkCopiedMessage, setLinkCopiedMessage] = useState<string | null>(
    null
  );
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<"success" | "error" | "info">(
    "success"
  );
  const router = useRouter();

  // Get invite data from Zustand store
  const {
    inviteStatuses,
    inviteLinks,
    inviteExpiries,
    documentsCompleted,
    setInviteStatus,
    setInviteLink,
    setInviteExpiry,
    setDocumentCompleted,
  } = useRegistrationStore();

  // Define registration steps - dynamically show 4 or 5 steps based on current step
  const registrationSteps =
    currentStep <= 4
      ? [
          { id: 1, label: "Company Details" },
          { id: 2, label: "Person Details" },
          { id: 3, label: "Address & Capital" },
          { id: 4, label: "Review & Submit" },
        ]
      : [
          { id: 1, label: "Company Details" },
          { id: 2, label: "Person Details" },
          { id: 3, label: "Address & Capital" },
          { id: 4, label: "Review & Submit" },
          { id: 5, label: "Upload Documents" },
        ];

  // Use Zustand registrationId
  const registrationId = zustandRegistrationId;
  const setRegistrationId = setZustandRegistrationId;

  // Person form for step 2
  const {
    control: personControl,
    handleSubmit: handlePersonSubmit,
    formState: { errors: personErrors },
    setValue: setPersonValue,
    watch: watchPersonForm,
  } = useForm<PersonDetailsFormData>({
    defaultValues: {
      persons: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: personControl,
    name: "persons",
  });

  // Load persons from Zustand to React Hook Form on mount
  useEffect(() => {
    if (persons.length > 0) {
      setPersonValue("persons", persons);
    }
  }, []);

  // Watch form changes and sync to Zustand store
  useEffect(() => {
    const subscription = watchPersonForm((value, { name }) => {
      if (name && name.startsWith("persons") && value.persons) {
        // Filter out undefined values and ensure complete PersonData objects
        const validPersons = value.persons.filter(
          (person): person is PersonData => {
            return person !== undefined && person !== null;
          }
        );

        // Update Zustand store when form changes
        setPersons(validPersons);
        updateLastSaved();
      }
    });

    return () => subscription.unsubscribe();
  }, [watchPersonForm, setPersons, updateLastSaved]);

  // Fetch invite statuses when registration ID is available
  useEffect(() => {
    const fetchInviteStatuses = async () => {
      if (!registrationId) return;

      try {
        const response = await axios.get(
          `/api/get-person-status?registrationId=${registrationId}`
        );

        if (response.data.success) {
          const personStatuses = response.data.data.personStatuses;

          // Convert API response to local state format
          const newInviteStatuses: Record<
            number,
            "none" | "pending" | "form_submitted" | "documents_submitted"
          > = {};
          const newDocumentsCompleted: Record<number, boolean> = {};
          const newInviteLinks: Record<number, string> = {};
          const newInviteExpiries: Record<number, Date> = {};

          Object.keys(personStatuses).forEach((index) => {
            const personIndex = parseInt(index);
            const status = personStatuses[personIndex];

            if (status.hasInvitation) {
              newInviteStatuses[personIndex] = status.inviteStatus;

              // Store invite link and expiry if available
              if (status.inviteLink) {
                newInviteLinks[personIndex] = status.inviteLink;
              }
              if (status.inviteExpiry) {
                newInviteExpiries[personIndex] = new Date(status.inviteExpiry);
              }
            } else {
              // For non-invited persons (Person A), set status to 'none'
              newInviteStatuses[personIndex] = "none";
            }

            // Track document completion status for all persons
            newDocumentsCompleted[personIndex] =
              status.documentsCompleted || false;
          });

          // Update Zustand store with all invite data
          Object.keys(newInviteStatuses).forEach((index) => {
            const personIndex = parseInt(index);
            setInviteStatus(personIndex, newInviteStatuses[personIndex]);
            setDocumentCompleted(
              personIndex,
              newDocumentsCompleted[personIndex]
            );
            if (newInviteLinks[personIndex]) {
              setInviteLink(personIndex, newInviteLinks[personIndex]);
            }
            if (newInviteExpiries[personIndex]) {
              setInviteExpiry(personIndex, newInviteExpiries[personIndex]);
            }
          });
        }
      } catch (error) {
        console.error("Error fetching invite statuses:", error);
      }
    };

    fetchInviteStatuses();
  }, [registrationId]);

  // Refresh status when user comes back to the page (focus event)
  useEffect(() => {
    const handleFocus = () => {
      if (registrationId && currentStep === 5) {
        console.log("Page focused, refreshing status...");
        // Re-fetch status when user comes back to the page
        const fetchInviteStatuses = async () => {
          try {
            const response = await axios.get(
              `/api/get-person-status?registrationId=${registrationId}`
            );

            if (response.data.success) {
              const personStatuses = response.data.data.personStatuses;
              const newInviteStatuses: Record<
                number,
                "none" | "pending" | "form_submitted" | "documents_submitted"
              > = {};
              const newDocumentsCompleted: Record<number, boolean> = {};
              const newInviteLinks: Record<number, string> = {};
              const newInviteExpiries: Record<number, Date> = {};

              Object.keys(personStatuses).forEach((index) => {
                const personIndex = parseInt(index);
                const status = personStatuses[personIndex];

                if (status.hasInvitation) {
                  newInviteStatuses[personIndex] = status.inviteStatus;

                  // Store invite link and expiry if available
                  if (status.inviteLink) {
                    newInviteLinks[personIndex] = status.inviteLink;
                  }
                  if (status.inviteExpiry) {
                    newInviteExpiries[personIndex] = new Date(
                      status.inviteExpiry
                    );
                  }
                } else {
                  newInviteStatuses[personIndex] = "none";
                }

                newDocumentsCompleted[personIndex] =
                  status.documentsCompleted || false;
              });

              // Update Zustand store with all invite data
              Object.keys(newInviteStatuses).forEach((index) => {
                const personIndex = parseInt(index);
                setInviteStatus(personIndex, newInviteStatuses[personIndex]);
                setDocumentCompleted(
                  personIndex,
                  newDocumentsCompleted[personIndex]
                );
                if (newInviteLinks[personIndex]) {
                  setInviteLink(personIndex, newInviteLinks[personIndex]);
                }
                if (newInviteExpiries[personIndex]) {
                  setInviteExpiry(personIndex, newInviteExpiries[personIndex]);
                }
              });
            }
          } catch (error) {
            console.error("Error refreshing status on focus:", error);
          }
        };

        fetchInviteStatuses();
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [registrationId, currentStep]);

  // Manual sync function for persons
  const syncPersons = useCallback(
    (persons: PersonData[]) => {
      setPersons(persons);
      // Also update React Hook Form
      setPersonValue("persons", persons);
      updateLastSaved();
    },
    [setPersons, setPersonValue, updateLastSaved]
  );

  // Function to clear all form data (useful for logout or starting fresh)
  const clearAllData = useCallback(() => {
    resetForm();
    remove();
    setRegistrationId("");
    localStorage.removeItem("selectedCompanyType");
    localStorage.removeItem("registrationId");
  }, [resetForm, remove, setRegistrationId]);

  const handleCompanyTypeSelect = (companyType: CompanyType) => {
    // Convert local CompanyType to Zustand CompanyType
    const zustandCompanyType = {
      id: companyType.id,
      name: companyType.name,
      code: companyType.code,
      description: companyType.description,
      minShareCapital:
        parseInt(companyType.minCapital.replace(/[^\d]/g, "")) || 0,
    };
    setCompanyType(zustandCompanyType);
    setShowModal(false);
    localStorage.setItem("selectedCompanyType", JSON.stringify(companyType));
  };

  const handleStep1Success = (registrationId: string) => {
    setRegistrationId(registrationId);
    localStorage.setItem("registrationId", registrationId);
    setCurrentStep(2);
  };

  useEffect(() => {
    // Check auth and load company type
    const checkAuth = async () => {
      try {
        await axios.get("/api/auth/me");

        // Load selected company type from localStorage
        const storedCompanyType = localStorage.getItem("selectedCompanyType");
        if (storedCompanyType && !selectedCompanyType) {
          const companyType = JSON.parse(storedCompanyType);
          const zustandCompanyType = {
            id: companyType.id,
            name: companyType.name,
            code: companyType.code,
            description: companyType.description,
            minShareCapital:
              parseInt(companyType.minCapital?.replace(/[^\d]/g, "")) || 0,
          };
          setCompanyType(zustandCompanyType);
        }

        // Load registration ID from localStorage
        const storedRegistrationId = localStorage.getItem("registrationId");
        if (storedRegistrationId && !zustandRegistrationId) {
          setZustandRegistrationId(storedRegistrationId);

          // Check registration status to determine correct step
          try {
            const statusResponse = await axios.get(
              `/api/get-registration-status?registrationId=${storedRegistrationId}`
            );
            if (statusResponse.data.success) {
              const status = statusResponse.data.status;
              console.log("Loaded registration status:", status);

              if (status === "COMPLETED") {
                router.push("/dashboard");
                return;
              } else if (status === "SUBMITTED") {
                console.log("Status is SUBMITTED, setting step to 5");
                setCurrentStep(5);
                setRegistrationStatus(status);
              } else {
                // IN_PROGRESS - will be handled by existing logic
                setRegistrationStatus(status);
              }
            }
          } catch (statusError) {
            console.error("Error fetching registration status:", statusError);
          }
        }
      } catch (err) {
        console.error("Auth check failed:", err);
        router.push("/login");
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [router, setZustandRegistrationId, setCurrentStep, setRegistrationStatus]);

  // Handle step navigation based on registration status
  useEffect(() => {
    if (registrationStatus === "COMPLETED") {
      // Registration is complete, redirect to dashboard
      router.push("/dashboard");
    } else if (registrationStatus === "SUBMITTED") {
      // Form is submitted, user should always be on document upload step
      console.log("Registration status is SUBMITTED, setting step to 5");
      setCurrentStep(5);
    }
  }, [registrationStatus, setCurrentStep, router]);

  // Check if modal should be shown after company type is loaded
  useEffect(() => {
    if (!loading && !selectedCompanyType && currentStep === 1) {
      setShowModal(true);
    }
  }, [loading, selectedCompanyType, currentStep]);

  // Helper function to get all persons that need document upload (fetch from database)
  const getPersonsNeedingDocuments = (): Array<{
    index: number;
    role: string;
    isCompleted: boolean;
  }> => {
    const personsNeedingDocs: Array<{
      index: number;
      role: string;
      isCompleted: boolean;
    }> = [];

    // Get actual persons from database by checking invite statuses
    // Only persons that exist in database will have entries in inviteStatuses
    Object.entries(inviteStatuses).forEach(([index, status]) => {
      const personIndex = parseInt(index);

      // Only include persons who need manual document upload
      // Exclude invited persons (they handle documents via invite flow)
      if (status === "none") {
        const isCompleted = documentsCompleted[personIndex] || false;

        personsNeedingDocs.push({
          index: personIndex,
          role:
            getPersonRoles(selectedCompanyType?.name || "")[personIndex] ||
            `Person ${personIndex + 1}`,
          isCompleted: isCompleted,
        });
      }
    });

    // Sort by index to maintain order (Person A first, then others)
    const sortedPersons = personsNeedingDocs.sort((a, b) => a.index - b.index);

    // Debug logging
    console.log("=== Document Upload Debug ===");
    console.log("Invite statuses (database persons):", inviteStatuses);
    console.log("Documents completed state:", documentsCompleted);
    console.log("Filtered persons needing manual upload:", sortedPersons);
    console.log("Total database persons:", Object.keys(inviteStatuses).length);
    console.log("Manual upload needed:", sortedPersons.length);

    return sortedPersons;
  };

  // Helper function to check if document upload form should be shown
  // Show form to Person A even if invited persons haven't completed (validation happens on submit)
  const areAllInvitationsCompleted = () => {
    const actualInvitations = Object.entries(inviteStatuses).filter(
      ([index, status]) => {
        const personIndex = parseInt(index);
        return personIndex !== 0 && status !== "none"; // Exclude Person A and non-invited persons
      }
    );

    // If no actual invitations exist, Person A can proceed
    if (actualInvitations.length === 0) return true;

    // Show form to Person A even if invitations are pending (submit button will validate)
    return true;
  };

  // Helper function to check if all persons (manual + Person A) have completed documents
  const areAllDocumentsCompleted = () => {
    const personsNeedingDocs = getPersonsNeedingDocuments();
    return personsNeedingDocs.every((person) => person.isCompleted);
  };

  // Helper functions for person details
  const getPersonRoles = (companyType: string) => {
    switch (companyType) {
      case "Private Limited Company":
        return ["Director 1", "Director 2"];
      case "One Person Company":
        return ["Owner", "Nominee"];
      case "Limited Liability Partnership":
        return ["Partner 1", "Partner 2"];
      case "Section-8 Company":
        return ["Director 1", "Director 2"];
      default:
        return ["Person 1", "Person 2"];
    }
  };

  const canAddMorePersons = (companyType: string) => {
    return companyType !== "One Person Company";
  };

  // Initialize persons when step 2 is reached
  const initializePersons = useCallback(async () => {
    if (!selectedCompanyType) return;

    // If persons already exist in Zustand but not in form, load them
    if (persons.length > 0 && fields.length === 0) {
      persons.forEach((person) => append(person));
      syncPersons(persons);
      return;
    }

    // If no persons exist, create initial ones
    if (persons.length === 0) {
      try {
        const response = await axios.get("/api/auth/me");
        const user = response.data.user;

        const roles = getPersonRoles(selectedCompanyType.name);
        const initialPersons: PersonData[] = roles.map((role, index) => ({
          firstName: index === 0 ? user.firstName : "",
          middleName: index === 0 ? user.middleName || "" : "",
          lastName: index === 0 ? user.lastName || "" : "",
          email: index === 0 ? user.email || "" : "",
          contact: index === 0 ? user.contact || "" : "",
          aadhaarVerified: false,
          panVerified: false,
          hasDIN: false,
          dinVerified: false,
        }));

        // Update both Zustand store and React Hook Form
        setPersons(initialPersons);
        syncPersons(initialPersons);

        // Clear existing and add new persons to form
        remove();
        initialPersons.forEach((person) => append(person));
      } catch (error) {
        console.error("Error initializing persons:", error);
      }
    }
  }, [
    selectedCompanyType,
    persons.length,
    setPersons,
    syncPersons,
    append,
    remove,
  ]);

  // Load existing persons from Zustand on page load
  useEffect(() => {
    if (persons.length > 0 && fields.length === 0) {
      // Load existing persons from Zustand into React Hook Form
      persons.forEach((person) => append(person));
      syncPersons(persons);
    }
  }, [persons, fields.length, append, syncPersons]);

  // Initialize persons when currentStep becomes 2
  useEffect(() => {
    if (currentStep === 2 && selectedCompanyType) {
      initializePersons();
    }
  }, [currentStep, selectedCompanyType, initializePersons]);

  // Person form functions
  const handleGetLink = async (personData: {
    personRole: string;
    personIndex: number;
  }) => {
    if (!registrationId) {
      setToastType("error");
      setToastMessage("Registration ID not found. Please refresh the page.");
      return;
    }

    try {
      const response = await axios.post("/api/send-person-invite", {
        registrationId,
        personRole: personData.personRole,
        personIndex: personData.personIndex,
      });

      if (response.data.success) {
        // Update invite status based on API response
        const inviteStatus = response.data.data.status || "pending";
        const inviteLink = response.data.data.inviteLink;
        const expiresAt = new Date(response.data.data.expiresAt);

        // Update Zustand store with invite data
        setInviteStatus(personData.personIndex, inviteStatus as any);
        setInviteLink(personData.personIndex, inviteLink);
        setInviteExpiry(personData.personIndex, expiresAt);

        console.log(
          `Updated invite status for person ${personData.personIndex}:`,
          inviteStatus
        );
        console.log("All invite statuses:", inviteStatuses);

        // Copy link to clipboard
        navigator.clipboard
          .writeText(inviteLink)
          .then(() => {
            setLinkCopiedMessage(
              ` Invite link copied! Expires: ${expiresAt.toLocaleDateString()}`
            );
            setTimeout(() => setLinkCopiedMessage(null), 5000); // Hide after 5 seconds
          })
          .catch(() => {
            prompt("Copy this link:", inviteLink);
          });
      }
    } catch (error) {
      console.error("Error generating link:", error);
      setToastType("error");
      setToastMessage("❌ Failed to generate link. Please try again.");
    }
  };

  const addPerson = () => {
    if (!selectedCompanyType || !canAddMorePersons(selectedCompanyType.name))
      return;
    if (fields.length >= 15) return;

    const newPerson: PersonData = {
      firstName: "",
      middleName: "",
      lastName: "",
      email: "",
      contact: "",
      aadhaarVerified: false,
      panVerified: false,
      hasDIN: false,
      dinVerified: false,
    };

    append(newPerson);
  };

  const removePerson = (index: number) => {
    if (index < 2) return;
    remove(index);
  };

  const onPersonSubmit = async (data: PersonDetailsFormData) => {
    if (!registrationId) {
      setToastType("error");
      setToastMessage("Registration ID not found. Please refresh the page.");
      return;
    }

    try {
      setLoading(true);

      const response = await axios.post("/api/save-person-details-step2", {
        persons: data.persons,
        companyType: selectedCompanyType?.name,
        registrationId: registrationId,
      });

      if (response.data.success) {
        setToastType("success");
        setToastMessage(
          " Person details saved successfully! Proceeding to next step..."
        );
        setCurrentStep(3);
      } else {
        throw new Error(response.data.error || "Failed to save data");
      }
    } catch (error) {
      console.error("Error saving person details:", error);
      setToastType("error");
      setToastMessage("❌ Failed to save person details. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Navbar />

      <div className="flex-1 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Step Progress Indicator */}
          <ProgressIndicator
            steps={registrationSteps}
            currentStep={currentStep}
          />

          {/* Submitted Form Protection */}
          {registrationStatus === "SUBMITTED" && currentStep < 4 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">✓</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-green-800">
                    Registration Submitted
                  </h3>
                  <p className="text-green-700">
                    Your registration has been successfully submitted and cannot
                    be edited.
                    <button
                      onClick={() => setCurrentStep(4)}
                      className="ml-2 text-green-600 underline hover:text-green-800"
                    >
                      View status →
                    </button>
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Step Content */}
          {currentStep === 1 && registrationStatus !== "SUBMITTED" && (
            <CompanyDetailsForm
              selectedCompanyType={selectedCompanyType}
              onSubmitSuccess={handleStep1Success}
              loading={loading}
              setLoading={setLoading}
              onChangeCompanyType={() => setShowModal(true)}
            />
          )}

          {/* Step 2: Person Details */}
          {currentStep === 2 && registrationStatus !== "SUBMITTED" && (
            <div className="space-y-8">
              {/* Header */}
              <div className="text-center">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Person Details
                </h1>
                {selectedCompanyType && (
                  <div className="flex items-center justify-center space-x-4 text-blue-600 mt-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">
                        {selectedCompanyType.icon}
                      </span>
                      <span className="font-medium">
                        {selectedCompanyType.name}
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => setShowModal(true)}
                      className="text-sm text-gray-500 hover:text-blue-600 underline"
                    >
                      Change
                    </button>
                  </div>
                )}

                {/* Data Persistence Indicator */}
              </div>

              {/* Company Type Info */}
              {selectedCompanyType && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800">
                    <strong>Company Type:</strong> {selectedCompanyType.name}
                  </p>
                </div>
              )}

              <form
                onSubmit={handlePersonSubmit(onPersonSubmit)}
                className="space-y-8"
              >
                {/* Person Forms */}
                {fields.map((field, index) => (
                  <PersonForm
                    key={field.id}
                    index={index}
                    role={
                      getPersonRoles(selectedCompanyType?.name || "")[index] ||
                      `Person ${index + 1}`
                    }
                    control={personControl}
                    errors={personErrors}
                    setValue={setPersonValue}
                    canRemove={index >= 2}
                    onRemove={() => removePerson(index)}
                    isFirstPerson={index === 0}
                    onGetLink={handleGetLink}
                    onLinkCopied={(message) => {
                      setLinkCopiedMessage(message);
                      setTimeout(() => setLinkCopiedMessage(null), 5000);
                    }}
                    inviteStatus={inviteStatuses[index] || "none"}
                    documentsCompleted={documentsCompleted[index] || false}
                    inviteLink={inviteLinks[index]}
                    inviteExpiry={
                      inviteExpiries[index]
                        ? new Date(inviteExpiries[index])
                        : undefined
                    }
                    onShowToast={(message, type) => {
                      setToastType(type);
                      setToastMessage(message);
                    }}
                  />
                ))}

                {/* Add Person Button */}
                {canAddMorePersons(selectedCompanyType?.name || "") &&
                  fields.length < 15 && (
                    <div className="text-center">
                      <button
                        type="button"
                        onClick={addPerson}
                        className="inline-flex items-center px-6 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                      >
                        <span className="mr-2">➕</span>
                        Add Person
                      </button>
                      <p className="text-sm text-gray-500 mt-2">
                        You can add up to {15 - fields.length} more persons
                      </p>
                    </div>
                  )}

                {/* Navigation Buttons */}
                <div className="flex justify-between pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => setCurrentStep(1)}
                    className="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  >
                    Previous
                  </button>

                  <button
                    type="submit"
                    disabled={loading}
                    className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Saving...</span>
                      </div>
                    ) : (
                      "Save & Continue"
                    )}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Step 3: Address & Capital */}
          {currentStep === 3 &&
            registrationId &&
            registrationStatus !== "SUBMITTED" && (
              <AddressCapitalForm
                selectedCompanyType={selectedCompanyType}
                registrationId={registrationId}
                onSubmitSuccess={() => setCurrentStep(4)}
                onPrevious={() => setCurrentStep(2)}
                onShowToast={(message, type) => {
                  setToastType(type);
                  setToastMessage(message);
                }}
              />
            )}

          {/* Step 3: Error State */}
          {currentStep === 3 &&
            !registrationId &&
            registrationStatus !== "SUBMITTED" && (
              <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                <div className="text-center">
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    Address & Capital
                  </h1>
                  {/* <p className="text-gray-600 mb-4">Step 3 of 4 - Configure address proof and share allocation</p> */}
                  <div className="text-red-600">
                    <p>
                      ⚠️ Registration ID not found. Please complete previous
                      steps first.
                    </p>
                    <button
                      onClick={() => setCurrentStep(1)}
                      className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      Go to Step 1
                    </button>
                  </div>
                </div>
              </div>
            )}

          {/* Step 4: Review & Submit */}
          {currentStep === 4 && (
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Review & Submit
                </h1>
                <p className="text-gray-600">
                  Step 4 of 5 - Review your information before submission
                </p>
              </div>

              {/* Registration Review Component */}
              {registrationId && (
                <div className="mb-8">
                  <RegistrationReview registrationId={registrationId} />
                </div>
              )}

              {/* Registration Status Component */}
              {registrationId && (
                <div className="mb-8">
                  <RegistrationStatus
                    registrationId={registrationId}
                    onStatusChange={(status) => {
                      // Only move to next step when user actually submits (not on initial load)
                      console.log("Status changed to:", status);
                      console.log("Current step before change:", currentStep);
                      console.log(
                        "Registration status before change:",
                        registrationStatus
                      );

                      // Only auto-advance if status changes to SUBMITTED and we weren't already SUBMITTED
                      if (
                        status === "SUBMITTED" &&
                        registrationStatus !== "SUBMITTED"
                      ) {
                        console.log(
                          "Status changed from non-SUBMITTED to SUBMITTED, advancing to step 5"
                        );
                        setCurrentStep(5);
                      } else {
                        console.log(
                          "Not advancing - either status was already SUBMITTED or not changing to SUBMITTED"
                        );
                      }
                    }}
                    onShowToast={(message, type) => {
                      setToastType(type);
                      setToastMessage(message);
                    }}
                  />
                </div>
              )}

              {/* Navigation */}
              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => setCurrentStep(3)}
                  disabled={registrationStatus === "SUBMITTED"}
                  className={`px-8 py-3 rounded-lg font-semibold transition-colors ${
                    registrationStatus === "SUBMITTED"
                      ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                      : "bg-gray-600 text-white hover:bg-gray-700"
                  }`}
                >
                  Previous
                </button>

                {registrationStatus === "SUBMITTED" && (
                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => setCurrentStep(5)}
                      className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <span>📄</span>
                      <span>Upload Documents</span>
                    </button>
                    <button
                      type="button"
                      onClick={() => router.push("/dashboard")}
                      className="bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-600"
                    >
                      Skip for Now
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 5: Document Upload */}
          {(() => {
            // Show Step 5 if currentStep is 5 and we have registrationId
            // Don't strictly require registrationStatus === 'SUBMITTED' as it might be updating
            const shouldShowStep5 = currentStep === 5 && registrationId;
            console.log("Step 5 render check:", {
              currentStep,
              registrationId,
              registrationStatus,
              shouldShowStep5,
            });
            return shouldShowStep5;
          })() && (
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  Upload Your Documents
                </h1>
                <p className="text-gray-600">
                  Step 5 of 5 - Upload required documents to complete
                  registration
                </p>

                {/* Refresh Status Button */}
                <button
                  onClick={async () => {
                    try {
                      const response = await axios.get(
                        `/api/get-person-status?registrationId=${registrationId}`
                      );
                      if (response.data.success) {
                        const personStatuses =
                          response.data.data.personStatuses;
                        const newInviteStatuses: Record<
                          number,
                          | "none"
                          | "pending"
                          | "form_submitted"
                          | "documents_submitted"
                        > = {};
                        const newDocumentsCompleted: Record<number, boolean> =
                          {};

                        Object.keys(personStatuses).forEach((index) => {
                          const personIndex = parseInt(index);
                          const status = personStatuses[personIndex];

                          if (status.hasInvitation) {
                            newInviteStatuses[personIndex] =
                              status.inviteStatus;
                          } else {
                            newInviteStatuses[personIndex] = "none";
                          }

                          newDocumentsCompleted[personIndex] =
                            status.documentsCompleted || false;
                        });

                        // Update Zustand store with refreshed data
                        Object.keys(newInviteStatuses).forEach((index) => {
                          const personIndex = parseInt(index);
                          setInviteStatus(
                            personIndex,
                            newInviteStatuses[personIndex]
                          );
                          setDocumentCompleted(
                            personIndex,
                            newDocumentsCompleted[personIndex]
                          );
                        });
                        setToastType("success");
                        setToastMessage("Status refreshed successfully!");
                      }
                    } catch (error) {
                      console.error("Error refreshing status:", error);
                      setToastType("error");
                      setToastMessage(
                        "❌ Failed to refresh status. Please try again."
                      );
                    }
                  }}
                  className="mt-4 text-blue-600 hover:text-blue-800 text-sm font-medium underline"
                >
                  🔄 Refresh Status
                </button>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">
                  📄 Document Upload Required
                </h3>
                <p className="text-blue-700 text-sm">
                  Please upload your required documents to complete the
                  registration process. This is the final step.
                </p>
              </div>

              {/* Invitation Status Check */}
              {(() => {
                // Filter to show only persons that actually have invitations (not 'none' status)
                const actualInvitations = Object.entries(inviteStatuses).filter(
                  ([index, status]) => {
                    const personIndex = parseInt(index);
                    return personIndex !== 0 && status !== "none"; // Exclude Person A and non-invited persons
                  }
                );

                return actualInvitations.length > 0;
              })() && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    👥 Invited Persons Status
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(inviteStatuses)
                      .filter(([index, status]) => {
                        const personIndex = parseInt(index);
                        return personIndex !== 0 && status !== "none"; // Only show actual invitations
                      })
                      .map(([index, status]) => {
                        const personIndex = parseInt(index);
                        const role =
                          getPersonRoles(selectedCompanyType?.name || "")[
                            personIndex
                          ] || `Person ${personIndex + 1}`;

                        return (
                          <div
                            key={index}
                            className="flex items-center justify-between p-3 bg-white rounded-lg border"
                          >
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <span className="text-gray-600 text-sm font-medium">
                                  {personIndex + 1}
                                </span>
                              </div>
                              <span className="font-medium text-gray-900">
                                {role}
                              </span>
                            </div>
                            <div className="flex items-center space-x-3">
                              <div>
                                {status === "pending" && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    ⏳ Invite Sent
                                  </span>
                                )}
                                {status === "form_submitted" && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    📝 Documents Pending
                                  </span>
                                )}
                                {status === "documents_submitted" && (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    ✅ Completed
                                  </span>
                                )}
                              </div>

                              {/* Copy Invite Link Button */}
                              {(status === "pending" ||
                                status === "form_submitted") &&
                                inviteLinks[personIndex] && (
                                  <button
                                    onClick={async () => {
                                      try {
                                        await navigator.clipboard.writeText(
                                          inviteLinks[personIndex]
                                        );
                                        // Show temporary success message
                                        const button = document.getElementById(
                                          `copy-btn-${personIndex}`
                                        );
                                        if (button) {
                                          const originalText = button.innerHTML;
                                          button.innerHTML = "✅ Copied!";
                                          button.classList.add(
                                            "bg-green-100",
                                            "text-green-700"
                                          );
                                          setTimeout(() => {
                                            button.innerHTML = originalText;
                                            button.classList.remove(
                                              "bg-green-100",
                                              "text-green-700"
                                            );
                                          }, 2000);
                                        }
                                      } catch (err) {
                                        console.error(
                                          "Failed to copy invite link:",
                                          err
                                        );
                                        alert("Failed to copy invite link");
                                      }
                                    }}
                                    id={`copy-btn-${personIndex}`}
                                    className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
                                    title="Copy invite link"
                                  >
                                    📋 Copy Link
                                  </button>
                                )}
                            </div>
                          </div>
                        );
                      })}
                  </div>

                  {/* Warning if any invitations are pending */}
                  {Object.entries(inviteStatuses)
                    .filter(([index, status]) => {
                      const personIndex = parseInt(index);
                      return personIndex !== 0 && status !== "none";
                    })
                    .some(([, status]) => status !== "documents_submitted") && (
                    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <span className="text-yellow-600 text-lg">⚠️</span>
                        <div>
                          <h4 className="text-yellow-800 font-medium">
                            Waiting for Invited Persons
                          </h4>
                          <p className="text-yellow-700 text-sm mt-1">
                            You can see the document upload form, but cannot
                            submit documents until all invited persons complete
                            their document submission.
                          </p>
                          <div className="mt-2 text-xs text-yellow-600">
                            <p>✅ Document upload form is visible</p>
                            <p>
                              ❌ Document submission blocked until all invited
                              persons complete
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* No Invitations Message */}
              {Object.entries(inviteStatuses).filter(([index, status]) => {
                const personIndex = parseInt(index);
                return personIndex !== 0 && status !== "none";
              }).length === 0 && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                  <div className="flex items-center space-x-3">
                    <span className="text-green-600 text-2xl">✅</span>
                    <div>
                      <h3 className="text-lg font-semibold text-green-800">
                        No Invitations Sent
                      </h3>
                      <p className="text-green-700 text-sm">
                        You haven't sent any invitations to other persons. You
                        can proceed to upload your documents directly.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Document Upload for All Persons or Completion Status */}
              {areAllDocumentsCompleted() ? (
                // Show thank you message if Person A has completed documents
                <div className="bg-green-50 border border-green-200 rounded-lg p-8 text-center">
                  <div className="text-green-600 text-6xl mb-6">🎉</div>
                  <h2 className="text-2xl font-bold text-green-800 mb-4">
                    Thank You!
                  </h2>
                  <h3 className="text-lg font-semibold text-green-700 mb-3">
                    All Details Submitted Successfully
                  </h3>
                  <p className="text-green-600 mb-6">
                    Your registration has been completed successfully. We have
                    received all your documents and information.
                  </p>
                  <div className="bg-white border border-green-200 rounded-lg p-4 mb-6">
                    <p className="text-green-700 font-medium">
                      📞 We will connect with you shortly to proceed with your
                      company registration.
                    </p>
                  </div>
                  <div className="flex justify-center space-x-4">
                    <button
                      onClick={() => router.push("/dashboard")}
                      className="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                    >
                      Go to Dashboard
                    </button>
                  </div>
                </div>
              ) : areAllInvitationsCompleted() ? (
                // Show document upload forms for all persons that need documents
                <div className="space-y-8">
                  {getPersonsNeedingDocuments()
                    .filter((person) => !person.isCompleted)
                    .map((person) => (
                      <div
                        key={person.index}
                        className="bg-gray-50 rounded-lg p-6"
                      >
                        <div className="mb-4">
                          <h3 className="text-lg font-semibold text-gray-900">
                            📄 Documents for {person.role}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            Upload required documents for {person.role}
                          </p>
                        </div>

                        <DocumentUpload
                          personRole={person.role}
                          registrationId={registrationId}
                          personIndex={person.index}
                          addressVerificationMethod={
                            persons[person.index]?.addressVerificationMethod
                          }
                          onSubmitSuccess={async () => {
                            // Refresh document status after successful upload
                            try {
                              const response = await axios.get(
                                `/api/get-person-status?registrationId=${registrationId}`
                              );
                              if (response.data.success) {
                                const personStatuses =
                                  response.data.data.personStatuses;
                                const newDocumentsCompleted: Record<
                                  number,
                                  boolean
                                > = {};

                                Object.keys(personStatuses).forEach((index) => {
                                  const personIndex = parseInt(index);
                                  const status = personStatuses[personIndex];
                                  newDocumentsCompleted[personIndex] =
                                    status.documentsCompleted || false;
                                });

                                // Update Zustand store with document completion status
                                Object.keys(newDocumentsCompleted).forEach(
                                  (index) => {
                                    const personIndex = parseInt(index);
                                    setDocumentCompleted(
                                      personIndex,
                                      newDocumentsCompleted[personIndex]
                                    );
                                  }
                                );
                              }
                            } catch (error) {
                              console.error(
                                "Error refreshing document status:",
                                error
                              );
                            }

                            // Show success message
                            setToastType("success");
                            setToastMessage(
                              `✅ Documents for ${person.role} have been uploaded successfully!`
                            );
                          }}
                          onShowToast={(message, type) => {
                            setToastType(type);
                            setToastMessage(message);
                          }}
                        />
                      </div>
                    ))}

                  {/* Progress Summary */}
                  {getPersonsNeedingDocuments().length > 1 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-blue-800 mb-4">
                        📊 Document Upload Progress
                      </h3>
                      <div className="space-y-2">
                        {getPersonsNeedingDocuments().map((person) => (
                          <div
                            key={person.index}
                            className="flex items-center justify-between"
                          >
                            <span className="text-blue-700">{person.role}</span>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${
                                person.isCompleted
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {person.isCompleted
                                ? "✅ Completed"
                                : "⏳ Pending"}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // Show waiting message if invitations are not completed
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-8 text-center">
                  <div className="text-yellow-600 text-4xl mb-4">⏳</div>
                  <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                    Waiting for Invited Persons
                  </h3>
                  <p className="text-yellow-700">
                    You cannot upload your documents until all invited persons
                    have completed their document submission.
                  </p>
                  <p className="text-yellow-600 text-sm mt-2">
                    Please check the status above and wait for all invited
                    persons to complete their part.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Company Type Modal */}
      <CompanyTypeModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onProceed={handleCompanyTypeSelect}
      />

      {/* Debug Component (only in development) */}
      {/* <RegistrationDebug /> */}

      {/* Toast Notifications */}
      <ToastNotification
        message={linkCopiedMessage}
        onClose={() => setLinkCopiedMessage(null)}
        type="success"
        duration={5000}
      />

      <ToastNotification
        message={toastMessage}
        onClose={() => setToastMessage(null)}
        type={toastType}
        duration={5000}
      />

      <Footer />
    </div>
  );
}
